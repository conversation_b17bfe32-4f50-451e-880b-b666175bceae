namespace TonLiker.Desktop.UI;

/// <summary>
/// Modern UI theme with contemporary colors and styling
/// </summary>
public static class ModernTheme
{
    // Primary Colors - Modern Blue Palette
    public static readonly Color Primary = Color.FromArgb(0, 120, 215);           // Modern Blue
    public static readonly Color PrimaryLight = Color.FromArgb(64, 158, 255);     // Light Blue
    public static readonly Color PrimaryDark = Color.FromArgb(0, 90, 158);        // Dark Blue
    public static readonly Color Accent = Color.FromArgb(0, 178, 148);            // Teal Accent

    // Background Colors - Dark Theme
    public static readonly Color BackgroundPrimary = Color.FromArgb(32, 32, 32);   // Dark Gray
    public static readonly Color BackgroundSecondary = Color.FromArgb(45, 45, 45); // Medium Gray
    public static readonly Color BackgroundTertiary = Color.FromArgb(60, 60, 60);  // Light Gray
    public static readonly Color Surface = Color.FromArgb(55, 55, 55);             // Surface Gray

    // Text Colors
    public static readonly Color TextPrimary = Color.FromArgb(255, 255, 255);      // White
    public static readonly Color TextSecondary = Color.FromArgb(200, 200, 200);    // Light Gray
    public static readonly Color TextMuted = Color.FromArgb(150, 150, 150);        // Muted Gray
    public static readonly Color TextDisabled = Color.FromArgb(100, 100, 100);     // Disabled Gray

    // Status Colors
    public static readonly Color Success = Color.FromArgb(16, 185, 129);           // Green
    public static readonly Color Warning = Color.FromArgb(245, 158, 11);           // Amber
    public static readonly Color Error = Color.FromArgb(239, 68, 68);              // Red
    public static readonly Color Info = Color.FromArgb(59, 130, 246);              // Blue

    // Border Colors
    public static readonly Color BorderPrimary = Color.FromArgb(75, 75, 75);       // Border Gray
    public static readonly Color BorderSecondary = Color.FromArgb(90, 90, 90);     // Light Border
    public static readonly Color BorderFocus = Primary;                            // Focus Border

    // Fonts
    public static readonly Font FontHeading = new("Segoe UI", 12F, FontStyle.Bold);
    public static readonly Font FontSubheading = new("Segoe UI", 10F, FontStyle.Bold);
    public static readonly Font FontBody = new("Segoe UI", 9F, FontStyle.Regular);
    public static readonly Font FontCaption = new("Segoe UI", 8F, FontStyle.Regular);
    public static readonly Font FontCode = new("Consolas", 9F, FontStyle.Regular);

    // Spacing
    public const int SpacingXS = 4;
    public const int SpacingSM = 8;
    public const int SpacingMD = 16;
    public const int SpacingLG = 24;
    public const int SpacingXL = 32;

    // Border Radius
    public const int BorderRadiusSM = 4;
    public const int BorderRadiusMD = 6;
    public const int BorderRadiusLG = 8;

    /// <summary>
    /// Apply modern theme to a Form
    /// </summary>
    public static void ApplyToForm(Form form)
    {
        form.BackColor = BackgroundPrimary;
        form.ForeColor = TextPrimary;
        form.Font = FontBody;
    }

    /// <summary>
    /// Apply modern theme to a TabControl
    /// </summary>
    public static void ApplyToTabControl(TabControl tabControl)
    {
        tabControl.BackColor = BackgroundSecondary;
        tabControl.ForeColor = TextPrimary;
        tabControl.Font = FontSubheading;
        
        foreach (TabPage tab in tabControl.TabPages)
        {
            tab.BackColor = BackgroundPrimary;
            tab.ForeColor = TextPrimary;
            tab.Font = FontBody;
        }
    }

    /// <summary>
    /// Apply modern theme to a GroupBox
    /// </summary>
    public static void ApplyToGroupBox(GroupBox groupBox)
    {
        groupBox.BackColor = BackgroundSecondary;
        groupBox.ForeColor = TextPrimary;
        groupBox.Font = FontSubheading;
        groupBox.FlatStyle = FlatStyle.Flat;
    }

    /// <summary>
    /// Apply modern theme to a Button
    /// </summary>
    public static void ApplyToButton(Button button, bool isPrimary = false)
    {
        button.FlatStyle = FlatStyle.Flat;
        button.Font = FontBody;
        button.Cursor = Cursors.Hand;
        
        if (isPrimary)
        {
            button.BackColor = Primary;
            button.ForeColor = Color.White;
            button.FlatAppearance.BorderColor = Primary;
        }
        else
        {
            button.BackColor = BackgroundTertiary;
            button.ForeColor = TextPrimary;
            button.FlatAppearance.BorderColor = BorderPrimary;
        }
        
        button.FlatAppearance.BorderSize = 1;
        button.FlatAppearance.MouseOverBackColor = isPrimary ? PrimaryLight : Surface;
        button.FlatAppearance.MouseDownBackColor = isPrimary ? PrimaryDark : BackgroundTertiary;
    }

    /// <summary>
    /// Apply modern theme to a TextBox
    /// </summary>
    public static void ApplyToTextBox(TextBox textBox)
    {
        textBox.BackColor = BackgroundTertiary;
        textBox.ForeColor = TextPrimary;
        textBox.Font = FontBody;
        textBox.BorderStyle = BorderStyle.FixedSingle;
    }

    /// <summary>
    /// Apply modern theme to a ListView
    /// </summary>
    public static void ApplyToListView(ListView listView)
    {
        listView.BackColor = BackgroundTertiary;
        listView.ForeColor = TextPrimary;
        listView.Font = FontBody;
        listView.BorderStyle = BorderStyle.FixedSingle;
        listView.OwnerDraw = false;
    }

    /// <summary>
    /// Apply modern theme to a RichTextBox
    /// </summary>
    public static void ApplyToRichTextBox(RichTextBox richTextBox)
    {
        richTextBox.BackColor = Color.FromArgb(20, 20, 20);
        richTextBox.ForeColor = TextSecondary;
        richTextBox.Font = FontCode;
        richTextBox.BorderStyle = BorderStyle.FixedSingle;
    }

    /// <summary>
    /// Apply modern theme to a ComboBox
    /// </summary>
    public static void ApplyToComboBox(ComboBox comboBox)
    {
        comboBox.BackColor = BackgroundTertiary;
        comboBox.ForeColor = TextPrimary;
        comboBox.Font = FontBody;
        comboBox.FlatStyle = FlatStyle.Flat;
    }

    /// <summary>
    /// Apply modern theme to a NumericUpDown
    /// </summary>
    public static void ApplyToNumericUpDown(NumericUpDown numericUpDown)
    {
        numericUpDown.BackColor = BackgroundTertiary;
        numericUpDown.ForeColor = TextPrimary;
        numericUpDown.Font = FontBody;
        numericUpDown.BorderStyle = BorderStyle.FixedSingle;
    }

    /// <summary>
    /// Apply modern theme to a CheckBox
    /// </summary>
    public static void ApplyToCheckBox(CheckBox checkBox)
    {
        checkBox.BackColor = Color.Transparent;
        checkBox.ForeColor = TextPrimary;
        checkBox.Font = FontBody;
        checkBox.FlatStyle = FlatStyle.Flat;
    }

    /// <summary>
    /// Apply modern theme to a Label
    /// </summary>
    public static void ApplyToLabel(Label label, bool isHeading = false)
    {
        label.BackColor = Color.Transparent;
        label.ForeColor = isHeading ? TextPrimary : TextSecondary;
        label.Font = isHeading ? FontSubheading : FontBody;
    }

    /// <summary>
    /// Apply modern theme to a StatusStrip
    /// </summary>
    public static void ApplyToStatusStrip(StatusStrip statusStrip)
    {
        statusStrip.BackColor = BackgroundSecondary;
        statusStrip.ForeColor = TextSecondary;
        statusStrip.Font = FontCaption;
    }
}
