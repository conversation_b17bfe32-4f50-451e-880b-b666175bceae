using System.Drawing.Drawing2D;

namespace TonLiker.Desktop.UI;

/// <summary>
/// Modern styled TabControl with custom drawing
/// </summary>
public class ModernTabControl : TabControl
{
    public ModernTabControl()
    {
        SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | 
                 ControlStyles.ResizeRedraw | ControlStyles.DoubleBuffer, true);
        
        DrawMode = TabDrawMode.OwnerDrawFixed;
        SizeMode = TabSizeMode.Fixed;
        ItemSize = new Size(120, 40);
        
        BackColor = ModernTheme.BackgroundPrimary;
        ForeColor = ModernTheme.TextPrimary;
        Font = ModernTheme.FontSubheading;
    }

    protected override void OnDrawItem(DrawItemEventArgs e)
    {
        var tabRect = GetTabRect(e.Index);
        var isSelected = e.Index == SelectedIndex;
        var isHovered = tabRect.Contains(PointToClient(MousePosition));

        // Background
        using (var brush = new SolidBrush(GetTabBackColor(isSelected, isHovered)))
        {
            e.Graphics.FillRectangle(brush, tabRect);
        }

        // Bottom border for selected tab
        if (isSelected)
        {
            using (var pen = new Pen(ModernTheme.Primary, 3))
            {
                e.Graphics.DrawLine(pen, tabRect.Left, tabRect.Bottom - 1, 
                                   tabRect.Right, tabRect.Bottom - 1);
            }
        }

        // Text
        var textColor = isSelected ? ModernTheme.TextPrimary : ModernTheme.TextSecondary;
        using (var brush = new SolidBrush(textColor))
        {
            var stringFormat = new StringFormat
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Center
            };
            
            e.Graphics.DrawString(TabPages[e.Index].Text, Font, brush, tabRect, stringFormat);
        }
    }

    protected override void OnPaint(PaintEventArgs e)
    {
        // Fill background
        using (var brush = new SolidBrush(ModernTheme.BackgroundSecondary))
        {
            e.Graphics.FillRectangle(brush, ClientRectangle);
        }

        // Draw tab area background
        var tabAreaRect = new Rectangle(0, 0, Width, ItemSize.Height);
        using (var brush = new SolidBrush(ModernTheme.BackgroundSecondary))
        {
            e.Graphics.FillRectangle(brush, tabAreaRect);
        }

        // Draw bottom border of tab area
        using (var pen = new Pen(ModernTheme.BorderPrimary))
        {
            e.Graphics.DrawLine(pen, 0, ItemSize.Height, Width, ItemSize.Height);
        }

        // Draw tabs
        for (int i = 0; i < TabCount; i++)
        {
            OnDrawItem(new DrawItemEventArgs(e.Graphics, Font, GetTabRect(i), i, DrawItemState.None));
        }
    }

    private Color GetTabBackColor(bool isSelected, bool isHovered)
    {
        if (isSelected)
            return ModernTheme.BackgroundPrimary;
        if (isHovered)
            return ModernTheme.Surface;
        return ModernTheme.BackgroundSecondary;
    }

    protected override void OnMouseMove(MouseEventArgs e)
    {
        base.OnMouseMove(e);
        Invalidate(); // Redraw for hover effects
    }

    protected override void OnMouseLeave(EventArgs e)
    {
        base.OnMouseLeave(e);
        Invalidate(); // Redraw to remove hover effects
    }
}
