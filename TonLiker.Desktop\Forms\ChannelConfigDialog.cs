using TonLiker.Models;

namespace TonLiker.Desktop.Forms;

public partial class ChannelConfigDialog : Form
{
    public ChannelConfiguration ChannelConfiguration { get; private set; }

    public ChannelConfigDialog() : this(new ChannelConfiguration())
    {
    }

    public ChannelConfigDialog(ChannelConfiguration? channel)
    {
        InitializeComponent();
        ChannelConfiguration = channel ?? new ChannelConfiguration();
        LoadConfiguration();
    }

    private void LoadConfiguration()
    {
        textBoxChannelName.Text = ChannelConfiguration.Name;
        textBoxProfilePath.Text = ChannelConfiguration.ProfilePath;
        checkBoxEnabled.Checked = ChannelConfiguration.IsEnabled;
        numericUpDownActionDelay.Value = ChannelConfiguration.DelayBetweenActions;
        numericUpDownScrollDelay.Value = ChannelConfiguration.ScrollDelay;
        numericUpDownMaxRetries.Value = ChannelConfiguration.MaxRetries;
        textBoxWalletAddress.Text = ChannelConfiguration.TonWalletAddress ?? "";
        
        // Load user agents
        textBoxUserAgents.Lines = ChannelConfiguration.UserAgents.ToArray();
    }

    private void SaveConfiguration()
    {
        ChannelConfiguration.Name = textBoxChannelName.Text.Trim();
        ChannelConfiguration.ProfilePath = textBoxProfilePath.Text.Trim();
        ChannelConfiguration.IsEnabled = checkBoxEnabled.Checked;
        ChannelConfiguration.DelayBetweenActions = (int)numericUpDownActionDelay.Value;
        ChannelConfiguration.ScrollDelay = (int)numericUpDownScrollDelay.Value;
        ChannelConfiguration.MaxRetries = (int)numericUpDownMaxRetries.Value;
        ChannelConfiguration.TonWalletAddress = string.IsNullOrWhiteSpace(textBoxWalletAddress.Text) 
            ? null : textBoxWalletAddress.Text.Trim();
            
        // Save user agents
        ChannelConfiguration.UserAgents.Clear();
        foreach (var line in textBoxUserAgents.Lines)
        {
            if (!string.IsNullOrWhiteSpace(line))
            {
                ChannelConfiguration.UserAgents.Add(line.Trim());
            }
        }
    }

    private void ButtonOK_Click(object? sender, EventArgs e)
    {
        if (ValidateInput())
        {
            SaveConfiguration();
            DialogResult = DialogResult.OK;
            Close();
        }
    }

    private void ButtonCancel_Click(object? sender, EventArgs e)
    {
        DialogResult = DialogResult.Cancel;
        Close();
    }

    private void ButtonBrowseProfile_Click(object? sender, EventArgs e)
    {
        using var dialog = new FolderBrowserDialog();
        dialog.Description = "Select Profile Directory";
        dialog.SelectedPath = textBoxProfilePath.Text;
        
        if (dialog.ShowDialog() == DialogResult.OK)
        {
            textBoxProfilePath.Text = dialog.SelectedPath;
        }
    }

    private bool ValidateInput()
    {
        if (string.IsNullOrWhiteSpace(textBoxChannelName.Text))
        {
            MessageBox.Show("Channel name is required.", "Validation Error", 
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
            textBoxChannelName.Focus();
            return false;
        }

        return true;
    }
}
