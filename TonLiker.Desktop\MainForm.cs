using TonLiker.Models;
using TonLiker.Services;
using Serilog;
using System.Collections.Concurrent;

namespace TonLiker.Desktop;

public partial class MainForm : Form
{
    private AppConfiguration? _config;
    private ILogger? _logger;
    private ConfigurationService? _configService;
    private ChromeSessionManager? _sessionManager;
    private SecurityService? _securityService;
    private NftAutomationService? _automationService;
    private FileImportService? _fileImportService;
    private ChannelOrchestrator? _orchestrator;

    private readonly ConcurrentDictionary<string, ChannelSession> _activeSessions = new();
    private readonly System.Threading.Timer _statusUpdateTimer;
    private bool _isProcessing = false;

    public MainForm()
    {
        InitializeComponent();
        InitializeServices();

        // Setup status update timer
        _statusUpdateTimer = new System.Threading.Timer(UpdateStatus, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));

        // Load configuration and update UI
        LoadConfiguration();
        UpdateConfigurationUI();
    }

    private void InitializeServices()
    {
        try
        {
            // Initialize configuration service
            _configService = new ConfigurationService(Log.Logger);
            _config = _configService.LoadConfiguration();

            // Initialize logging
            _logger = LoggingService.ConfigureLogging(_config);
            Log.Logger = _logger;

            // Initialize core services
            _securityService = new SecurityService(_config, _logger);
            _sessionManager = new ChromeSessionManager(_config, _logger);
            _automationService = new NftAutomationService(_config, _securityService, _logger);
            _fileImportService = new FileImportService(_securityService, _logger);
            _orchestrator = new ChannelOrchestrator(_config, _sessionManager, _automationService, _fileImportService, _logger);

            _logger.Information("TonLiker Desktop initialized successfully");
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Failed to initialize services: {ex.Message}", "Initialization Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void LoadConfiguration()
    {
        try
        {
            _config = _configService.LoadConfiguration();
            _logger?.Information("Configuration loaded successfully");
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Failed to load configuration");
            MessageBox.Show($"Failed to load configuration: {ex.Message}", "Configuration Error",
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void UpdateStatus(object? state)
    {
        if (InvokeRequired)
        {
            Invoke(new Action<object?>(UpdateStatus), state);
            return;
        }

        try
        {
            UpdateChannelStatus();
            UpdateProcessingStatus();
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error updating status");
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _statusUpdateTimer?.Dispose();
            _orchestrator?.Dispose();
            _sessionManager?.CloseAllSessionsAsync().Wait();
            _securityService?.Dispose();
            components?.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Configuration Tab Event Handlers

    private void ButtonBrowseSessionData_Click(object? sender, EventArgs e)
    {
        using var dialog = new FolderBrowserDialog();
        dialog.Description = "Select Session Data Directory";
        dialog.SelectedPath = textBoxSessionDataPath.Text;

        if (dialog.ShowDialog() == DialogResult.OK)
        {
            textBoxSessionDataPath.Text = dialog.SelectedPath;
        }
    }

    private void ButtonBrowseLogs_Click(object? sender, EventArgs e)
    {
        using var dialog = new FolderBrowserDialog();
        dialog.Description = "Select Logs Directory";
        dialog.SelectedPath = textBoxLogsPath.Text;

        if (dialog.ShowDialog() == DialogResult.OK)
        {
            textBoxLogsPath.Text = dialog.SelectedPath;
        }
    }

    private void ButtonBrowseInputFiles_Click(object? sender, EventArgs e)
    {
        using var dialog = new FolderBrowserDialog();
        dialog.Description = "Select Input Files Directory";
        dialog.SelectedPath = textBoxInputFilesPath.Text;

        if (dialog.ShowDialog() == DialogResult.OK)
        {
            textBoxInputFilesPath.Text = dialog.SelectedPath;
        }
    }

    private void UpdateConfigurationUI()
    {
        if (_config == null) return;

        textBoxSessionDataPath.Text = _config.SessionDataPath;
        textBoxLogsPath.Text = _config.LogsPath;
        textBoxInputFilesPath.Text = _config.InputFilesPath;
        numericUpDownMaxChannels.Value = _config.MaxConcurrentChannels;
        numericUpDownGlobalDelay.Value = _config.GlobalDelayBetweenChannels;
        checkBoxHeadlessMode.Checked = _config.EnableHeadlessMode;
        checkBoxDomainValidation.Checked = _config.EnableDomainValidation;
    }

    private void UpdateChannelStatus()
    {
        // Update channel status display
        // This will be implemented when we add the channel management UI
    }

    private void UpdateProcessingStatus()
    {
        // Update processing status display
        // This will be implemented when we add the processing UI
    }

    #endregion
}
