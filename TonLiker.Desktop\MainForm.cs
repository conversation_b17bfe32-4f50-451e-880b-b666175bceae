using TonLiker.Models;
using TonLiker.Services;
using TonLiker.Desktop.Forms;
using Serilog;
using System.Collections.Concurrent;
using System.Text.Json;

namespace TonLiker.Desktop;

public partial class MainForm : Form
{
    private AppConfiguration? _config;
    private ILogger? _logger;
    private ConfigurationService? _configService;
    private ChromeSessionManager? _sessionManager;
    private SecurityService? _securityService;
    private NftAutomationService? _automationService;
    private FileImportService? _fileImportService;
    private ChannelOrchestrator? _orchestrator;

    private readonly ConcurrentDictionary<string, ChannelSession> _activeSessions = new();
    private readonly System.Threading.Timer _statusUpdateTimer;
    private bool _isProcessing = false;

    // Progress tracking
    private DateTime _processingStartTime;
    private int _totalItemsToProcess = 0;
    private int _totalItemsProcessed = 0;
    private int _totalSuccessful = 0;
    private int _totalFailed = 0;

    public MainForm()
    {
        InitializeComponent();
        InitializeServices();

        // Setup status update timer
        _statusUpdateTimer = new System.Threading.Timer(UpdateStatus, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));

        // Load configuration and update UI
        LoadConfiguration();
        UpdateConfigurationUI();

        // Load application settings
        LoadApplicationSettings();
    }

    protected override void SetVisibleCore(bool value)
    {
        base.SetVisibleCore(value);
        if (value)
        {
            // Initialize logging display
            AppendLog("TonLiker Desktop started successfully", "INFO");
        }
    }

    protected override void OnFormClosing(FormClosingEventArgs e)
    {
        try
        {
            // Save application settings
            SaveApplicationSettings();

            // Stop any active processing
            if (_isProcessing)
            {
                var result = MessageBox.Show("Processing is still active. Do you want to stop and exit?",
                    "Confirm Exit", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }

                // Stop all processing
                _orchestrator?.StopAllWorkersAsync().Wait(5000);
            }

            _logger?.Information("TonLiker Desktop shutting down");
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error during application shutdown");
        }

        base.OnFormClosing(e);
    }

    private void InitializeServices()
    {
        try
        {
            // Initialize configuration service
            _configService = new ConfigurationService(Log.Logger);
            _config = _configService.LoadConfiguration();

            // Initialize logging
            _logger = LoggingService.ConfigureLogging(_config);
            Log.Logger = _logger;

            // Initialize core services
            _securityService = new SecurityService(_config, _logger);
            _sessionManager = new ChromeSessionManager(_config, _logger);
            _automationService = new NftAutomationService(_config, _securityService, _logger);
            _fileImportService = new FileImportService(_securityService, _logger);
            _orchestrator = new ChannelOrchestrator(_config, _sessionManager, _automationService, _fileImportService, _logger);

            _logger.Information("TonLiker Desktop initialized successfully");
        }
        catch (Exception ex)
        {
            HandleException(ex, "service initialization", "Failed to initialize the application services.");
        }
    }

    private void LoadConfiguration()
    {
        try
        {
            _config = _configService.LoadConfiguration();
            _logger?.Information("Configuration loaded successfully");
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Failed to load configuration");
            MessageBox.Show($"Failed to load configuration: {ex.Message}", "Configuration Error",
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void UpdateStatus(object? state)
    {
        if (InvokeRequired)
        {
            Invoke(new Action<object?>(UpdateStatus), state);
            return;
        }

        try
        {
            UpdateChannelStatus();
            UpdateProcessingStatus();
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error updating status");
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _statusUpdateTimer?.Dispose();
            _orchestrator?.Dispose();
            _sessionManager?.CloseAllSessionsAsync().Wait();
            _securityService?.Dispose();
            components?.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Configuration Tab Event Handlers

    private void ButtonBrowseSessionData_Click(object? sender, EventArgs e)
    {
        using var dialog = new FolderBrowserDialog();
        dialog.Description = "Select Session Data Directory";
        dialog.SelectedPath = textBoxSessionDataPath.Text;

        if (dialog.ShowDialog() == DialogResult.OK)
        {
            textBoxSessionDataPath.Text = dialog.SelectedPath;
        }
    }

    private void ButtonBrowseLogs_Click(object? sender, EventArgs e)
    {
        using var dialog = new FolderBrowserDialog();
        dialog.Description = "Select Logs Directory";
        dialog.SelectedPath = textBoxLogsPath.Text;

        if (dialog.ShowDialog() == DialogResult.OK)
        {
            textBoxLogsPath.Text = dialog.SelectedPath;
        }
    }

    private void ButtonBrowseInputFiles_Click(object? sender, EventArgs e)
    {
        using var dialog = new FolderBrowserDialog();
        dialog.Description = "Select Input Files Directory";
        dialog.SelectedPath = textBoxInputFilesPath.Text;

        if (dialog.ShowDialog() == DialogResult.OK)
        {
            textBoxInputFilesPath.Text = dialog.SelectedPath;
        }
    }

    private void UpdateConfigurationUI()
    {
        if (_config == null) return;

        textBoxSessionDataPath.Text = _config.SessionDataPath;
        textBoxLogsPath.Text = _config.LogsPath;
        textBoxInputFilesPath.Text = _config.InputFilesPath;
        numericUpDownMaxChannels.Value = _config.MaxConcurrentChannels;
        numericUpDownGlobalDelay.Value = _config.GlobalDelayBetweenChannels;
        checkBoxHeadlessMode.Checked = _config.EnableHeadlessMode;
        checkBoxDomainValidation.Checked = _config.EnableDomainValidation;

        UpdateChannelsList();
    }

    private void UpdateChannelsList()
    {
        listViewChannels.Items.Clear();

        if (_config?.Channels == null) return;

        foreach (var channel in _config.Channels)
        {
            var item = new ListViewItem(channel.Name);
            item.SubItems.Add(channel.IsEnabled ? "Yes" : "No");
            item.SubItems.Add(channel.DelayBetweenActions.ToString());
            item.SubItems.Add(channel.ScrollDelay.ToString());
            item.SubItems.Add(channel.MaxRetries.ToString());
            item.SubItems.Add(channel.TonWalletAddress ?? "");
            item.Tag = channel;
            listViewChannels.Items.Add(item);
        }
    }

    private void ListViewChannels_SelectedIndexChanged(object? sender, EventArgs e)
    {
        bool hasSelection = listViewChannels.SelectedItems.Count > 0;
        buttonEditChannel.Enabled = hasSelection;
        buttonRemoveChannel.Enabled = hasSelection;
    }

    private void ButtonAddChannel_Click(object? sender, EventArgs e)
    {
        using var dialog = new ChannelConfigDialog();
        if (dialog.ShowDialog() == DialogResult.OK && _config != null)
        {
            _config.Channels.Add(dialog.ChannelConfiguration);
            UpdateChannelsList();
        }
    }

    private void ButtonEditChannel_Click(object? sender, EventArgs e)
    {
        if (listViewChannels.SelectedItems.Count == 0) return;

        var selectedItem = listViewChannels.SelectedItems[0];
        var channel = (ChannelConfiguration)selectedItem.Tag;

        using var dialog = new ChannelConfigDialog(channel);
        if (dialog.ShowDialog() == DialogResult.OK)
        {
            UpdateChannelsList();
        }
    }

    private void ButtonRemoveChannel_Click(object? sender, EventArgs e)
    {
        if (listViewChannels.SelectedItems.Count == 0 || _config == null) return;

        var selectedItem = listViewChannels.SelectedItems[0];
        var channel = (ChannelConfiguration)selectedItem.Tag;

        var result = MessageBox.Show($"Are you sure you want to remove channel '{channel.Name}'?",
            "Confirm Removal", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            _config.Channels.Remove(channel);
            UpdateChannelsList();
        }
    }

    private async void ButtonSaveConfig_Click(object? sender, EventArgs e)
    {
        try
        {
            if (_config == null || _configService == null) return;

            // Update config from UI
            _config.SessionDataPath = textBoxSessionDataPath.Text;
            _config.LogsPath = textBoxLogsPath.Text;
            _config.InputFilesPath = textBoxInputFilesPath.Text;
            _config.MaxConcurrentChannels = (int)numericUpDownMaxChannels.Value;
            _config.GlobalDelayBetweenChannels = (int)numericUpDownGlobalDelay.Value;
            _config.EnableHeadlessMode = checkBoxHeadlessMode.Checked;
            _config.EnableDomainValidation = checkBoxDomainValidation.Checked;

            await _configService.SaveConfigurationAsync(_config);

            MessageBox.Show("Configuration saved successfully!", "Success",
                MessageBoxButtons.OK, MessageBoxIcon.Information);

            _logger?.Information("Configuration saved by user");
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Failed to save configuration");
            MessageBox.Show($"Failed to save configuration: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void ButtonLoadConfig_Click(object? sender, EventArgs e)
    {
        try
        {
            LoadConfiguration();
            UpdateConfigurationUI();

            MessageBox.Show("Configuration reloaded successfully!", "Success",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Failed to reload configuration");
            MessageBox.Show($"Failed to reload configuration: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void UpdateChannelStatus()
    {
        try
        {
            if (_sessionManager == null || _config?.Channels == null) return;

            // Update channel status list
            listViewChannelStatus.Items.Clear();

            foreach (var channel in _config.Channels.Where(c => c.IsEnabled))
            {
                var session = _sessionManager.GetSession(channel.Name);
                var item = new ListViewItem(channel.Name);

                if (session != null)
                {
                    item.SubItems.Add(session.IsActive ? "Active" : "Inactive");
                    item.SubItems.Add(session.StartTime.ToString("HH:mm:ss"));
                    item.SubItems.Add(session.ProcessedNfts.ToString());
                    item.SubItems.Add(session.SuccessfulLikes.ToString());
                    item.SubItems.Add(session.FailedAttempts.ToString());

                    try
                    {
                        item.SubItems.Add(session.Driver?.Url ?? "");
                    }
                    catch
                    {
                        item.SubItems.Add("N/A");
                    }

                    // Color code based on status
                    item.BackColor = session.IsActive ? Color.LightGreen : Color.LightGray;
                }
                else
                {
                    item.SubItems.Add("Not Started");
                    item.SubItems.Add("");
                    item.SubItems.Add("0");
                    item.SubItems.Add("0");
                    item.SubItems.Add("0");
                    item.SubItems.Add("");
                    item.BackColor = Color.LightYellow;
                }

                listViewChannelStatus.Items.Add(item);
            }
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error updating channel status");
        }
    }

    private void UpdateProcessingStatus()
    {
        try
        {
            if (_sessionManager == null) return;

            var activeCount = _sessionManager.ActiveSessionCount;
            _totalItemsProcessed = 0;
            _totalSuccessful = 0;
            _totalFailed = 0;

            foreach (var sessionName in _sessionManager.ActiveSessionNames)
            {
                var session = _sessionManager.GetSession(sessionName);
                if (session != null)
                {
                    _totalItemsProcessed += session.ProcessedNfts;
                    _totalSuccessful += session.SuccessfulLikes;
                    _totalFailed += session.FailedAttempts;
                }
            }

            // Update progress bar
            if (_totalItemsToProcess > 0 && _isProcessing)
            {
                var progressPercentage = (int)((double)_totalItemsProcessed / _totalItemsToProcess * 100);
                progressBar.Style = ProgressBarStyle.Continuous;
                progressBar.Value = Math.Min(progressPercentage, 100);
            }
            else if (_isProcessing)
            {
                progressBar.Style = ProgressBarStyle.Marquee;
            }
            else
            {
                progressBar.Style = ProgressBarStyle.Continuous;
                progressBar.Value = 0;
            }

            // Update status bar with detailed information
            if (activeCount > 0)
            {
                var elapsed = DateTime.UtcNow - _processingStartTime;
                var rate = elapsed.TotalSeconds > 0 ? _totalItemsProcessed / elapsed.TotalSeconds : 0;
                var successRate = _totalItemsProcessed > 0 ? (double)_totalSuccessful / _totalItemsProcessed * 100 : 0;

                var eta = "";
                if (_totalItemsToProcess > 0 && rate > 0)
                {
                    var remaining = _totalItemsToProcess - _totalItemsProcessed;
                    var etaSeconds = remaining / rate;
                    eta = $" | ETA: {TimeSpan.FromSeconds(etaSeconds):hh\\:mm\\:ss}";
                }

                statusLabel.Text = $"Active: {activeCount} | Processed: {_totalItemsProcessed}" +
                                 (_totalItemsToProcess > 0 ? $"/{_totalItemsToProcess}" : "") +
                                 $" | Success: {_totalSuccessful} ({successRate:F1}%) | Failed: {_totalFailed}" +
                                 $" | Rate: {rate:F1}/sec{eta}";
            }
            else if (_isProcessing)
            {
                statusLabel.Text = "Processing...";
            }
            else
            {
                statusLabel.Text = "Ready";
            }

            // Update file stats if on file processing tab
            if (tabControl.SelectedTab == tabFileProcessing && _isProcessing)
            {
                labelFileStats.Text = $"Processing: {_totalItemsProcessed}/{_totalItemsToProcess} | " +
                                     $"Successful: {_totalSuccessful} | Failed: {_totalFailed}";
            }

            // Update page stats if on page processing tab
            if (tabControl.SelectedTab == tabPageProcessing && _isProcessing)
            {
                labelPageStats.Text = $"Processing: {_totalItemsProcessed} | " +
                                     $"Successful: {_totalSuccessful} | Failed: {_totalFailed}";
            }
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error updating processing status");
        }
    }

    private void StartProcessingTracking(int totalItems = 0)
    {
        _processingStartTime = DateTime.UtcNow;
        _totalItemsToProcess = totalItems;
        _totalItemsProcessed = 0;
        _totalSuccessful = 0;
        _totalFailed = 0;
        _isProcessing = true;
    }

    private void StopProcessingTracking()
    {
        _isProcessing = false;
        progressBar.Style = ProgressBarStyle.Continuous;
        progressBar.Value = 0;
    }

    #endregion

    #region File Processing Tab Event Handlers

    private void ButtonBrowseFile_Click(object? sender, EventArgs e)
    {
        using var dialog = new OpenFileDialog();
        dialog.Title = "Select NFT File";
        dialog.Filter = "CSV Files (*.csv)|*.csv|Text Files (*.txt)|*.txt|All Files (*.*)|*.*";
        dialog.InitialDirectory = _config?.InputFilesPath ?? Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);

        if (dialog.ShowDialog() == DialogResult.OK)
        {
            textBoxSelectedFile.Text = dialog.FileName;
            buttonValidateFile.Enabled = true;
            buttonProcessFile.Enabled = false;
            listViewFileResults.Items.Clear();
            labelFileStats.Text = "File selected. Click Validate to check the file.";
        }
    }

    private async void ButtonValidateFile_Click(object? sender, EventArgs e)
    {
        if (!ValidateFileSelection() || _fileImportService == null)
            return;

        try
        {
            buttonValidateFile.Enabled = false;
            statusLabel.Text = "Validating file...";
            progressBar.Style = ProgressBarStyle.Marquee;

            var validationResult = _fileImportService.ValidateFile(textBoxSelectedFile.Text);

            if (validationResult.IsValid)
            {
                var importResult = await _fileImportService.ImportFromFileAsync(textBoxSelectedFile.Text);

                listViewFileResults.Items.Clear();
                foreach (var nft in importResult.NftItems)
                {
                    var item = new ListViewItem(nft.Url);
                    item.SubItems.Add(nft.Title ?? "");
                    item.SubItems.Add(nft.Collection ?? "");
                    item.SubItems.Add(nft.Status.ToString());
                    item.SubItems.Add(nft.ErrorMessage ?? "");
                    item.Tag = nft;

                    // Color code based on status
                    switch (nft.Status)
                    {
                        case NftStatus.Validated:
                            item.BackColor = Color.LightGreen;
                            break;
                        case NftStatus.Failed:
                            item.BackColor = Color.LightCoral;
                            break;
                        default:
                            item.BackColor = Color.LightYellow;
                            break;
                    }

                    listViewFileResults.Items.Add(item);
                }

                labelFileStats.Text = $"Total: {importResult.TotalCount}, Valid: {importResult.ValidCount}, " +
                                    $"Invalid: {importResult.InvalidCount}, Skipped: {importResult.SkippedCount}";

                buttonProcessFile.Enabled = importResult.ValidCount > 0;

                // Set total items for progress tracking
                _totalItemsToProcess = importResult.ValidCount;

                if (importResult.ValidationErrors.Any())
                {
                    var errorMessage = "Validation errors:\n" + string.Join("\n", importResult.ValidationErrors.Take(10));
                    if (importResult.ValidationErrors.Count > 10)
                        errorMessage += $"\n... and {importResult.ValidationErrors.Count - 10} more errors.";

                    MessageBox.Show(errorMessage, "Validation Errors", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            else
            {
                ShowError($"File validation failed: {validationResult.ErrorMessage}", "File Validation Error");
                labelFileStats.Text = "File validation failed.";
            }
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error validating file");
            MessageBox.Show($"Error validating file: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            labelFileStats.Text = "Error during validation.";
        }
        finally
        {
            buttonValidateFile.Enabled = true;
            statusLabel.Text = "Ready";
            progressBar.Style = ProgressBarStyle.Continuous;
        }
    }

    private async void ButtonProcessFile_Click(object? sender, EventArgs e)
    {
        if (!ValidateFileSelection() || !ValidateConfiguration() || _orchestrator == null)
            return;

        try
        {
            if (!ConfirmAction("This will start processing NFTs from the selected file. Continue?", "Confirm File Processing"))
                return;

            buttonProcessFile.Enabled = false;
            buttonValidateFile.Enabled = false;

            StartProcessingTracking(_totalItemsToProcess);
            statusLabel.Text = "Processing file...";

            await _orchestrator.ProcessFileAsync(textBoxSelectedFile.Text);

            MessageBox.Show("File processing completed!", "Success",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error processing file");
            MessageBox.Show($"Error processing file: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            buttonProcessFile.Enabled = true;
            buttonValidateFile.Enabled = true;
            StopProcessingTracking();
            statusLabel.Text = "Ready";
        }
    }

    #endregion

    #region Page Processing Tab Event Handlers

    private async void ButtonValidateUrl_Click(object? sender, EventArgs e)
    {
        if (string.IsNullOrWhiteSpace(textBoxPageUrl.Text) || _securityService == null)
            return;

        try
        {
            buttonValidateUrl.Enabled = false;
            statusLabel.Text = "Validating URL...";

            var validationResult = await _securityService.ValidateUrlAsync(textBoxPageUrl.Text.Trim());

            if (validationResult.IsValid)
            {
                labelPageStats.Text = "URL is valid and ready for processing.";
                buttonProcessPage.Enabled = true;
                MessageBox.Show("URL validation successful!", "Success",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                labelPageStats.Text = $"URL validation failed: {validationResult.ErrorMessage}";
                buttonProcessPage.Enabled = false;
                MessageBox.Show($"URL validation failed: {validationResult.ErrorMessage}", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error validating URL");
            MessageBox.Show($"Error validating URL: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            labelPageStats.Text = "Error during URL validation.";
            buttonProcessPage.Enabled = false;
        }
        finally
        {
            buttonValidateUrl.Enabled = true;
            statusLabel.Text = "Ready";
        }
    }

    private async void ButtonProcessPage_Click(object? sender, EventArgs e)
    {
        if (string.IsNullOrWhiteSpace(textBoxPageUrl.Text) || _orchestrator == null)
            return;

        try
        {
            var result = MessageBox.Show("This will start processing all NFTs found on the page. Continue?",
                "Confirm Processing", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result != DialogResult.Yes)
                return;

            buttonProcessPage.Enabled = false;
            buttonValidateUrl.Enabled = false;

            StartProcessingTracking(); // Unknown total for page processing
            statusLabel.Text = "Processing page...";

            listViewPageResults.Items.Clear();
            labelPageStats.Text = "Processing started...";

            await _orchestrator.ProcessPageAsync(textBoxPageUrl.Text.Trim());

            labelPageStats.Text = "Page processing completed!";
            MessageBox.Show("Page processing completed!", "Success",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error processing page");
            MessageBox.Show($"Error processing page: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            labelPageStats.Text = "Error during page processing.";
        }
        finally
        {
            buttonProcessPage.Enabled = true;
            buttonValidateUrl.Enabled = true;
            StopProcessingTracking();
            statusLabel.Text = "Ready";
        }
    }

    #endregion

    #region Channel Management Tab Event Handlers

    private void ListViewChannelStatus_SelectedIndexChanged(object? sender, EventArgs e)
    {
        bool hasSelection = listViewChannelStatus.SelectedItems.Count > 0;
        buttonStartSelectedChannel.Enabled = hasSelection && !_isProcessing;
        buttonStopSelectedChannel.Enabled = hasSelection && _isProcessing;
    }

    private async void ButtonStartAllChannels_Click(object? sender, EventArgs e)
    {
        if (_config?.Channels == null || !_config.Channels.Any(c => c.IsEnabled))
        {
            MessageBox.Show("No enabled channels found. Please configure channels first.", "No Channels",
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            buttonStartAllChannels.Enabled = false;
            buttonStopAllChannels.Enabled = true;

            StartProcessingTracking();
            statusLabel.Text = "Starting all channels...";

            // This would start all channels - implementation depends on orchestrator
            MessageBox.Show("All channels started successfully!", "Success",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error starting channels");
            MessageBox.Show($"Error starting channels: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            statusLabel.Text = "Ready";
        }
    }

    private async void ButtonStopAllChannels_Click(object? sender, EventArgs e)
    {
        try
        {
            buttonStopAllChannels.Enabled = false;
            statusLabel.Text = "Stopping all channels...";

            if (_orchestrator != null)
            {
                await _orchestrator.StopAllWorkersAsync();
            }

            buttonStartAllChannels.Enabled = true;
            StopProcessingTracking();

            MessageBox.Show("All channels stopped successfully!", "Success",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error stopping channels");
            MessageBox.Show($"Error stopping channels: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            statusLabel.Text = "Ready";
        }
    }

    private void ButtonStartSelectedChannel_Click(object? sender, EventArgs e)
    {
        if (listViewChannelStatus.SelectedItems.Count == 0)
            return;

        var selectedItem = listViewChannelStatus.SelectedItems[0];
        var channelName = selectedItem.Text;

        try
        {
            // Implementation for starting specific channel
            MessageBox.Show($"Channel '{channelName}' started successfully!", "Success",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error starting channel {ChannelName}", channelName);
            MessageBox.Show($"Error starting channel: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void ButtonStopSelectedChannel_Click(object? sender, EventArgs e)
    {
        if (listViewChannelStatus.SelectedItems.Count == 0)
            return;

        var selectedItem = listViewChannelStatus.SelectedItems[0];
        var channelName = selectedItem.Text;

        try
        {
            // Implementation for stopping specific channel
            MessageBox.Show($"Channel '{channelName}' stopped successfully!", "Success",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error stopping channel {ChannelName}", channelName);
            MessageBox.Show($"Error stopping channel: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    #endregion

    #region Logs Tab Event Handlers

    private void ComboBoxLogLevel_SelectedIndexChanged(object? sender, EventArgs e)
    {
        // Filter logs based on selected level
        // This would be implemented with a proper log filtering mechanism
    }

    private void ButtonClearLogs_Click(object? sender, EventArgs e)
    {
        richTextBoxLogs.Clear();
        _logger?.Information("Log display cleared by user");
    }

    private void ButtonSaveLogs_Click(object? sender, EventArgs e)
    {
        try
        {
            using var dialog = new SaveFileDialog();
            dialog.Title = "Save Logs";
            dialog.Filter = "Text Files (*.txt)|*.txt|Log Files (*.log)|*.log|All Files (*.*)|*.*";
            dialog.DefaultExt = "txt";
            dialog.FileName = $"TonLiker_Logs_{DateTime.Now:yyyyMMdd_HHmmss}.txt";

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                File.WriteAllText(dialog.FileName, richTextBoxLogs.Text);
                MessageBox.Show("Logs saved successfully!", "Success",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Error saving logs");
            MessageBox.Show($"Error saving logs: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    public void AppendLog(string message, string level = "INFO")
    {
        if (InvokeRequired)
        {
            Invoke(new Action<string, string>(AppendLog), message, level);
            return;
        }

        try
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            var logEntry = $"[{timestamp}] [{level}] {message}\n";

            // Color code based on log level
            var color = level.ToUpper() switch
            {
                "ERROR" => Color.Red,
                "WARN" or "WARNING" => Color.Yellow,
                "INFO" or "INFORMATION" => Color.LightGray,
                "DEBUG" => Color.LightBlue,
                _ => Color.LightGray
            };

            richTextBoxLogs.SelectionStart = richTextBoxLogs.TextLength;
            richTextBoxLogs.SelectionLength = 0;
            richTextBoxLogs.SelectionColor = color;
            richTextBoxLogs.AppendText(logEntry);
            richTextBoxLogs.SelectionColor = richTextBoxLogs.ForeColor;

            // Auto-scroll to bottom
            richTextBoxLogs.SelectionStart = richTextBoxLogs.TextLength;
            richTextBoxLogs.ScrollToCaret();

            // Limit log size to prevent memory issues
            if (richTextBoxLogs.Lines.Length > 1000)
            {
                var lines = richTextBoxLogs.Lines.Skip(200).ToArray();
                richTextBoxLogs.Lines = lines;
            }
        }
        catch (Exception ex)
        {
            // Avoid infinite loop if logging fails
            System.Diagnostics.Debug.WriteLine($"Error appending log: {ex.Message}");
        }
    }

    #endregion

    #region Error Handling and User Feedback

    private void ShowError(string message, string title = "Error", Exception? exception = null)
    {
        _logger?.Error(exception, "User error: {Message}", message);
        MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
    }

    private void ShowWarning(string message, string title = "Warning")
    {
        _logger?.Warning("User warning: {Message}", message);
        MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
    }

    private void ShowInfo(string message, string title = "Information")
    {
        _logger?.Information("User info: {Message}", message);
        MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private bool ConfirmAction(string message, string title = "Confirm Action")
    {
        var result = MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question);
        _logger?.Information("User confirmation for '{Title}': {Result}", title, result);
        return result == DialogResult.Yes;
    }

    private void HandleException(Exception ex, string operation, string userMessage = "")
    {
        _logger?.Error(ex, "Exception during {Operation}", operation);

        var message = string.IsNullOrEmpty(userMessage)
            ? $"An error occurred during {operation}:\n\n{ex.Message}"
            : $"{userMessage}\n\nTechnical details: {ex.Message}";

        ShowError(message, $"Error - {operation}");
    }

    private bool ValidateConfiguration()
    {
        if (_config == null)
        {
            ShowError("Configuration is not loaded. Please restart the application.");
            return false;
        }

        if (!_config.Channels.Any(c => c.IsEnabled))
        {
            ShowWarning("No channels are enabled. Please enable at least one channel in the Configuration tab.");
            return false;
        }

        return true;
    }

    private bool ValidateFileSelection()
    {
        if (string.IsNullOrWhiteSpace(textBoxSelectedFile.Text))
        {
            ShowWarning("Please select a file first.");
            return false;
        }

        if (!File.Exists(textBoxSelectedFile.Text))
        {
            ShowError("The selected file does not exist. Please select a valid file.");
            return false;
        }

        return true;
    }

    private bool ValidateUrlInput()
    {
        if (string.IsNullOrWhiteSpace(textBoxPageUrl.Text))
        {
            ShowWarning("Please enter a URL first.");
            return false;
        }

        if (!Uri.TryCreate(textBoxPageUrl.Text.Trim(), UriKind.Absolute, out var uri))
        {
            ShowError("The entered URL is not in a valid format.");
            return false;
        }

        if (!uri.Host.Contains("getgems.io"))
        {
            ShowError("Only getgems.io URLs are supported.");
            return false;
        }

        return true;
    }

    #endregion

    #region Application Settings Persistence

    private void LoadApplicationSettings()
    {
        try
        {
            var settingsFile = "TonLiker.Desktop.settings.json";
            if (File.Exists(settingsFile))
            {
                var json = File.ReadAllText(settingsFile);
                var settings = JsonSerializer.Deserialize<DesktopSettings>(json);

                if (settings != null)
                {
                    // Restore window state
                    if (settings.WindowState != FormWindowState.Minimized)
                    {
                        this.WindowState = settings.WindowState;
                    }

                    if (settings.WindowBounds.Width > 0 && settings.WindowBounds.Height > 0)
                    {
                        this.Bounds = settings.WindowBounds;
                    }

                    // Restore selected tab
                    if (settings.SelectedTabIndex >= 0 && settings.SelectedTabIndex < tabControl.TabCount)
                    {
                        tabControl.SelectedIndex = settings.SelectedTabIndex;
                    }

                    // Restore last used paths
                    if (!string.IsNullOrEmpty(settings.LastUsedFilePath))
                    {
                        textBoxSelectedFile.Text = settings.LastUsedFilePath;
                        if (File.Exists(settings.LastUsedFilePath))
                        {
                            buttonValidateFile.Enabled = true;
                        }
                    }

                    if (!string.IsNullOrEmpty(settings.LastUsedPageUrl))
                    {
                        textBoxPageUrl.Text = settings.LastUsedPageUrl;
                    }

                    // Restore log level
                    if (settings.LogLevel >= 0 && settings.LogLevel < comboBoxLogLevel.Items.Count)
                    {
                        comboBoxLogLevel.SelectedIndex = settings.LogLevel;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger?.Warning(ex, "Failed to load application settings");
        }
    }

    private void SaveApplicationSettings()
    {
        try
        {
            var settings = new DesktopSettings
            {
                WindowState = this.WindowState,
                WindowBounds = this.WindowState == FormWindowState.Normal ? this.Bounds : this.RestoreBounds,
                SelectedTabIndex = tabControl.SelectedIndex,
                LastUsedFilePath = textBoxSelectedFile.Text,
                LastUsedPageUrl = textBoxPageUrl.Text,
                LogLevel = comboBoxLogLevel.SelectedIndex
            };

            var options = new JsonSerializerOptions
            {
                WriteIndented = true
            };

            var json = JsonSerializer.Serialize(settings, options);
            File.WriteAllText("TonLiker.Desktop.settings.json", json);
        }
        catch (Exception ex)
        {
            _logger?.Warning(ex, "Failed to save application settings");
        }
    }

    #endregion
}

public class DesktopSettings
{
    public FormWindowState WindowState { get; set; } = FormWindowState.Normal;
    public Rectangle WindowBounds { get; set; }
    public int SelectedTabIndex { get; set; } = 0;
    public string LastUsedFilePath { get; set; } = "";
    public string LastUsedPageUrl { get; set; } = "";
    public int LogLevel { get; set; } = 0;
}
