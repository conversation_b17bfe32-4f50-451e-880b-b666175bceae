namespace TonLiker.Desktop.Models;

/// <summary>
/// Result of URL validation
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; private set; }
    public string? ErrorMessage { get; private set; }

    private ValidationResult(bool isValid, string? errorMessage = null)
    {
        IsValid = isValid;
        ErrorMessage = errorMessage;
    }

    public static ValidationResult Valid() => new(true);
    public static ValidationResult Invalid(string errorMessage) => new(false, errorMessage);
}

/// <summary>
/// Result of NFT processing
/// </summary>
public class NftProcessingResult
{
    public bool Success { get; private set; }
    public string Message { get; private set; } = string.Empty;

    private NftProcessingResult(bool success, string message)
    {
        Success = success;
        Message = message;
    }

    public static NftProcessingResult CreateSuccess(string message = "Success") => new(true, message);
    public static NftProcessingResult CreateFailed(string message) => new(false, message);
}

/// <summary>
/// Result of page processing
/// </summary>
public class PageProcessingResult
{
    public bool Success { get; set; } = true;
    public string ErrorMessage { get; set; } = string.Empty;
    public int ProcessedCount { get; set; } = 0;
    public int LikedCount { get; set; } = 0;
    public int FailedCount { get; set; } = 0;
    public int AlreadyLikedCount { get; set; } = 0;

    public static PageProcessingResult Failed(string errorMessage) => new()
    {
        Success = false,
        ErrorMessage = errorMessage
    };
}

/// <summary>
/// Result of file import operation
/// </summary>
public class ImportResult
{
    public bool Success { get; set; } = false;
    public string ErrorMessage { get; set; } = string.Empty;
    public int TotalCount { get; set; } = 0;
    public int ValidCount { get; set; } = 0;
    public int InvalidCount { get; set; } = 0;
    public int SkippedCount { get; set; } = 0;
    public List<NftItem> NftItems { get; set; } = new();
    public List<string> ValidationErrors { get; set; } = new();

    public static ImportResult Failed(string errorMessage) => new()
    {
        Success = false,
        ErrorMessage = errorMessage
    };
}

/// <summary>
/// Result of file validation
/// </summary>
public class FileValidationResult
{
    public bool IsValid { get; private set; }
    public string? ErrorMessage { get; private set; }

    private FileValidationResult(bool isValid, string? errorMessage = null)
    {
        IsValid = isValid;
        ErrorMessage = errorMessage;
    }

    public static FileValidationResult Valid() => new(true);
    public static FileValidationResult Invalid(string errorMessage) => new(false, errorMessage);
}

/// <summary>
/// CSV record for importing NFT data
/// </summary>
public class NftCsvRecord
{
    public string Url { get; set; } = string.Empty;
    public string? Title { get; set; }
    public string? Collection { get; set; }
}
