{"sessionDataPath": "./SessionData", "logsPath": "./Logs", "inputFilesPath": "./InputFiles", "maxConcurrentChannels": 3, "globalDelayBetweenChannels": 5000, "enableHeadlessMode": false, "enableDomainValidation": true, "allowedDomains": ["getgems.io"], "channels": [{"name": "Channel1", "profilePath": "", "isEnabled": true, "delayBetweenActions": 1000, "scrollDelay": 2000, "maxRetries": 3, "tonWalletAddress": null, "customHeaders": {}, "userAgents": []}], "chrome": {"chromeDriverPath": null, "chromeBinaryPath": null, "pageLoadTimeout": 30, "implicitWaitTimeout": 10, "defaultArguments": ["--disable-blink-features=AutomationControlled", "--disable-extensions", "--no-sandbox", "--disable-dev-shm-usage", "--disable-gpu", "--remote-debugging-port=0"]}, "security": {"validateNftUrls": true, "preventCrossDomainActions": true, "maxUrlValidationRetries": 3, "urlValidationTimeout": 10, "blockedDomains": [], "suspiciousPatterns": ["phishing", "scam", "fake", "malicious"]}}