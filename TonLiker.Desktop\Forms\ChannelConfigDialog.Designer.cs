namespace TonLiker.Desktop.Forms;

partial class ChannelConfigDialog
{
    private System.ComponentModel.IContainer components = null;

    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    private void InitializeComponent()
    {
        this.labelChannelName = new Label();
        this.textBoxChannelName = new TextBox();
        this.labelProfilePath = new Label();
        this.textBoxProfilePath = new TextBox();
        this.buttonBrowseProfile = new Button();
        this.checkBoxEnabled = new CheckBox();
        this.labelActionDelay = new Label();
        this.numericUpDownActionDelay = new NumericUpDown();
        this.labelScrollDelay = new Label();
        this.numericUpDownScrollDelay = new NumericUpDown();
        this.labelMaxRetries = new Label();
        this.numericUpDownMaxRetries = new NumericUpDown();
        this.labelWalletAddress = new Label();
        this.textBoxWalletAddress = new TextBox();
        this.labelUserAgents = new Label();
        this.textBoxUserAgents = new TextBox();
        this.buttonOK = new Button();
        this.buttonCancel = new Button();
        
        this.SuspendLayout();
        
        // Form
        this.AutoScaleDimensions = new SizeF(7F, 15F);
        this.AutoScaleMode = AutoScaleMode.Font;
        this.ClientSize = new Size(500, 450);
        this.Text = "Channel Configuration";
        this.FormBorderStyle = FormBorderStyle.FixedDialog;
        this.MaximizeBox = false;
        this.MinimizeBox = false;
        this.StartPosition = FormStartPosition.CenterParent;
        
        // Channel Name
        this.labelChannelName.Text = "Channel Name:";
        this.labelChannelName.Location = new Point(12, 15);
        this.labelChannelName.Size = new Size(100, 23);
        
        this.textBoxChannelName.Location = new Point(120, 12);
        this.textBoxChannelName.Size = new Size(200, 23);
        
        // Profile Path
        this.labelProfilePath.Text = "Profile Path:";
        this.labelProfilePath.Location = new Point(12, 45);
        this.labelProfilePath.Size = new Size(100, 23);
        
        this.textBoxProfilePath.Location = new Point(120, 42);
        this.textBoxProfilePath.Size = new Size(280, 23);
        
        this.buttonBrowseProfile.Text = "Browse";
        this.buttonBrowseProfile.Location = new Point(410, 41);
        this.buttonBrowseProfile.Size = new Size(70, 25);
        this.buttonBrowseProfile.Click += ButtonBrowseProfile_Click;
        
        // Enabled
        this.checkBoxEnabled.Text = "Enabled";
        this.checkBoxEnabled.Location = new Point(120, 75);
        this.checkBoxEnabled.Size = new Size(80, 23);
        this.checkBoxEnabled.Checked = true;
        
        // Action Delay
        this.labelActionDelay.Text = "Action Delay (ms):";
        this.labelActionDelay.Location = new Point(12, 105);
        this.labelActionDelay.Size = new Size(100, 23);
        
        this.numericUpDownActionDelay.Location = new Point(120, 102);
        this.numericUpDownActionDelay.Size = new Size(80, 23);
        this.numericUpDownActionDelay.Minimum = 500;
        this.numericUpDownActionDelay.Maximum = 10000;
        this.numericUpDownActionDelay.Value = 1000;
        this.numericUpDownActionDelay.Increment = 100;
        
        // Scroll Delay
        this.labelScrollDelay.Text = "Scroll Delay (ms):";
        this.labelScrollDelay.Location = new Point(220, 105);
        this.labelScrollDelay.Size = new Size(100, 23);
        
        this.numericUpDownScrollDelay.Location = new Point(330, 102);
        this.numericUpDownScrollDelay.Size = new Size(80, 23);
        this.numericUpDownScrollDelay.Minimum = 1000;
        this.numericUpDownScrollDelay.Maximum = 10000;
        this.numericUpDownScrollDelay.Value = 2000;
        this.numericUpDownScrollDelay.Increment = 100;
        
        // Max Retries
        this.labelMaxRetries.Text = "Max Retries:";
        this.labelMaxRetries.Location = new Point(12, 135);
        this.labelMaxRetries.Size = new Size(100, 23);
        
        this.numericUpDownMaxRetries.Location = new Point(120, 132);
        this.numericUpDownMaxRetries.Size = new Size(60, 23);
        this.numericUpDownMaxRetries.Minimum = 1;
        this.numericUpDownMaxRetries.Maximum = 10;
        this.numericUpDownMaxRetries.Value = 3;
        
        // Wallet Address
        this.labelWalletAddress.Text = "TON Wallet Address:";
        this.labelWalletAddress.Location = new Point(12, 165);
        this.labelWalletAddress.Size = new Size(120, 23);
        
        this.textBoxWalletAddress.Location = new Point(140, 162);
        this.textBoxWalletAddress.Size = new Size(340, 23);
        
        // User Agents
        this.labelUserAgents.Text = "User Agents (one per line):";
        this.labelUserAgents.Location = new Point(12, 195);
        this.labelUserAgents.Size = new Size(150, 23);
        
        this.textBoxUserAgents.Location = new Point(12, 220);
        this.textBoxUserAgents.Size = new Size(468, 150);
        this.textBoxUserAgents.Multiline = true;
        this.textBoxUserAgents.ScrollBars = ScrollBars.Vertical;
        
        // Buttons
        this.buttonOK.Text = "OK";
        this.buttonOK.Location = new Point(325, 385);
        this.buttonOK.Size = new Size(75, 30);
        this.buttonOK.Click += ButtonOK_Click;
        
        this.buttonCancel.Text = "Cancel";
        this.buttonCancel.Location = new Point(405, 385);
        this.buttonCancel.Size = new Size(75, 30);
        this.buttonCancel.Click += ButtonCancel_Click;
        
        // Add controls to form
        this.Controls.AddRange(new Control[] {
            this.labelChannelName, this.textBoxChannelName,
            this.labelProfilePath, this.textBoxProfilePath, this.buttonBrowseProfile,
            this.checkBoxEnabled,
            this.labelActionDelay, this.numericUpDownActionDelay,
            this.labelScrollDelay, this.numericUpDownScrollDelay,
            this.labelMaxRetries, this.numericUpDownMaxRetries,
            this.labelWalletAddress, this.textBoxWalletAddress,
            this.labelUserAgents, this.textBoxUserAgents,
            this.buttonOK, this.buttonCancel
        });
        
        this.ResumeLayout(false);
        this.PerformLayout();
    }

    #endregion
    
    private Label labelChannelName;
    private TextBox textBoxChannelName;
    private Label labelProfilePath;
    private TextBox textBoxProfilePath;
    private Button buttonBrowseProfile;
    private CheckBox checkBoxEnabled;
    private Label labelActionDelay;
    private NumericUpDown numericUpDownActionDelay;
    private Label labelScrollDelay;
    private NumericUpDown numericUpDownScrollDelay;
    private Label labelMaxRetries;
    private NumericUpDown numericUpDownMaxRetries;
    private Label labelWalletAddress;
    private TextBox textBoxWalletAddress;
    private Label labelUserAgents;
    private TextBox textBoxUserAgents;
    private Button buttonOK;
    private Button buttonCancel;
}
