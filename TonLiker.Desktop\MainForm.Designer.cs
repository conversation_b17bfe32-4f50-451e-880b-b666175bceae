﻿namespace TonLiker.Desktop;

partial class MainForm
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;



    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        this.tabControl = new TabControl();
        this.tabConfiguration = new TabPage();
        this.tabFileProcessing = new TabPage();
        this.tabPageProcessing = new TabPage();
        this.tabChannelManagement = new TabPage();
        this.tabLogs = new TabPage();
        this.statusStrip = new StatusStrip();
        this.statusLabel = new ToolStripStatusLabel();
        this.progressBar = new ToolStripProgressBar();

        // Configuration Tab Controls
        this.groupBoxPaths = new GroupBox();
        this.labelSessionDataPath = new Label();
        this.textBoxSessionDataPath = new TextBox();
        this.buttonBrowseSessionData = new Button();
        this.labelLogsPath = new Label();
        this.textBoxLogsPath = new TextBox();
        this.buttonBrowseLogs = new Button();
        this.labelInputFilesPath = new Label();
        this.textBoxInputFilesPath = new TextBox();
        this.buttonBrowseInputFiles = new Button();

        this.groupBoxGeneral = new GroupBox();
        this.labelMaxChannels = new Label();
        this.numericUpDownMaxChannels = new NumericUpDown();
        this.labelGlobalDelay = new Label();
        this.numericUpDownGlobalDelay = new NumericUpDown();
        this.checkBoxHeadlessMode = new CheckBox();
        this.checkBoxDomainValidation = new CheckBox();

        this.groupBoxChannels = new GroupBox();
        this.listViewChannels = new ListView();
        this.buttonAddChannel = new Button();
        this.buttonEditChannel = new Button();
        this.buttonRemoveChannel = new Button();
        this.buttonSaveConfig = new Button();
        this.buttonLoadConfig = new Button();

        // File Processing Tab Controls
        this.groupBoxFileSelection = new GroupBox();
        this.labelSelectedFile = new Label();
        this.textBoxSelectedFile = new TextBox();
        this.buttonBrowseFile = new Button();
        this.buttonValidateFile = new Button();
        this.buttonProcessFile = new Button();

        this.groupBoxFileResults = new GroupBox();
        this.listViewFileResults = new ListView();
        this.labelFileStats = new Label();

        // Page Processing Tab Controls
        this.groupBoxPageUrl = new GroupBox();
        this.labelPageUrl = new Label();
        this.textBoxPageUrl = new TextBox();
        this.buttonValidateUrl = new Button();
        this.buttonProcessPage = new Button();

        this.groupBoxPageResults = new GroupBox();
        this.listViewPageResults = new ListView();
        this.labelPageStats = new Label();

        // Channel Management Tab Controls
        this.groupBoxChannelControl = new GroupBox();
        this.buttonStartAllChannels = new Button();
        this.buttonStopAllChannels = new Button();
        this.buttonStartSelectedChannel = new Button();
        this.buttonStopSelectedChannel = new Button();

        this.groupBoxChannelStatus = new GroupBox();
        this.listViewChannelStatus = new ListView();

        // Logs Tab Controls
        this.groupBoxLogDisplay = new GroupBox();
        this.richTextBoxLogs = new RichTextBox();
        this.buttonClearLogs = new Button();
        this.buttonSaveLogs = new Button();
        this.comboBoxLogLevel = new ComboBox();
        this.labelLogLevel = new Label();

        this.SuspendLayout();

        // Main Form
        this.AutoScaleDimensions = new SizeF(7F, 15F);
        this.AutoScaleMode = AutoScaleMode.Font;
        this.ClientSize = new Size(1200, 800);
        this.Text = "TonLiker Desktop - NFT Automation Tool";
        this.StartPosition = FormStartPosition.CenterScreen;
        this.MinimumSize = new Size(1000, 600);

        // Tab Control
        this.tabControl.Dock = DockStyle.Fill;
        this.tabControl.Location = new Point(0, 0);
        this.tabControl.Size = new Size(1200, 775);
        this.tabControl.TabIndex = 0;

        // Configuration Tab
        this.tabConfiguration.Text = "Configuration";
        this.tabConfiguration.UseVisualStyleBackColor = true;

        // Configuration Tab Layout
        SetupConfigurationTab();

        // File Processing Tab
        this.tabFileProcessing.Text = "File Processing";
        this.tabFileProcessing.UseVisualStyleBackColor = true;

        // Page Processing Tab
        this.tabPageProcessing.Text = "Page Processing";
        this.tabPageProcessing.UseVisualStyleBackColor = true;

        // Channel Management Tab
        this.tabChannelManagement.Text = "Channel Management";
        this.tabChannelManagement.UseVisualStyleBackColor = true;

        // Logs Tab
        this.tabLogs.Text = "Logs";
        this.tabLogs.UseVisualStyleBackColor = true;

        // Status Strip
        this.statusStrip.Items.AddRange(new ToolStripItem[] { this.statusLabel, this.progressBar });
        this.statusStrip.Location = new Point(0, 775);
        this.statusStrip.Size = new Size(1200, 25);
        this.statusStrip.TabIndex = 1;

        this.statusLabel.Text = "Ready";
        this.progressBar.Size = new Size(200, 16);

        // Add tabs to tab control
        this.tabControl.TabPages.Add(this.tabConfiguration);
        this.tabControl.TabPages.Add(this.tabFileProcessing);
        this.tabControl.TabPages.Add(this.tabPageProcessing);
        this.tabControl.TabPages.Add(this.tabChannelManagement);
        this.tabControl.TabPages.Add(this.tabLogs);

        // Add controls to form
        this.Controls.Add(this.tabControl);
        this.Controls.Add(this.statusStrip);

        this.ResumeLayout(false);
        this.PerformLayout();
    }

    private void SetupConfigurationTab()
    {
        // Paths Group Box
        this.groupBoxPaths.Text = "Paths";
        this.groupBoxPaths.Location = new Point(10, 10);
        this.groupBoxPaths.Size = new Size(560, 120);

        this.labelSessionDataPath.Text = "Session Data Path:";
        this.labelSessionDataPath.Location = new Point(10, 25);
        this.labelSessionDataPath.Size = new Size(120, 23);

        this.textBoxSessionDataPath.Location = new Point(140, 22);
        this.textBoxSessionDataPath.Size = new Size(350, 23);

        this.buttonBrowseSessionData.Text = "Browse";
        this.buttonBrowseSessionData.Location = new Point(500, 21);
        this.buttonBrowseSessionData.Size = new Size(50, 25);
        this.buttonBrowseSessionData.Click += ButtonBrowseSessionData_Click;

        this.labelLogsPath.Text = "Logs Path:";
        this.labelLogsPath.Location = new Point(10, 55);
        this.labelLogsPath.Size = new Size(120, 23);

        this.textBoxLogsPath.Location = new Point(140, 52);
        this.textBoxLogsPath.Size = new Size(350, 23);

        this.buttonBrowseLogs.Text = "Browse";
        this.buttonBrowseLogs.Location = new Point(500, 51);
        this.buttonBrowseLogs.Size = new Size(50, 25);
        this.buttonBrowseLogs.Click += ButtonBrowseLogs_Click;

        this.labelInputFilesPath.Text = "Input Files Path:";
        this.labelInputFilesPath.Location = new Point(10, 85);
        this.labelInputFilesPath.Size = new Size(120, 23);

        this.textBoxInputFilesPath.Location = new Point(140, 82);
        this.textBoxInputFilesPath.Size = new Size(350, 23);

        this.buttonBrowseInputFiles.Text = "Browse";
        this.buttonBrowseInputFiles.Location = new Point(500, 81);
        this.buttonBrowseInputFiles.Size = new Size(50, 25);
        this.buttonBrowseInputFiles.Click += ButtonBrowseInputFiles_Click;

        this.groupBoxPaths.Controls.AddRange(new Control[] {
            this.labelSessionDataPath, this.textBoxSessionDataPath, this.buttonBrowseSessionData,
            this.labelLogsPath, this.textBoxLogsPath, this.buttonBrowseLogs,
            this.labelInputFilesPath, this.textBoxInputFilesPath, this.buttonBrowseInputFiles
        });

        // General Settings Group Box
        this.groupBoxGeneral.Text = "General Settings";
        this.groupBoxGeneral.Location = new Point(580, 10);
        this.groupBoxGeneral.Size = new Size(300, 120);

        this.labelMaxChannels.Text = "Max Channels:";
        this.labelMaxChannels.Location = new Point(10, 25);
        this.labelMaxChannels.Size = new Size(100, 23);

        this.numericUpDownMaxChannels.Location = new Point(120, 22);
        this.numericUpDownMaxChannels.Size = new Size(60, 23);
        this.numericUpDownMaxChannels.Minimum = 1;
        this.numericUpDownMaxChannels.Maximum = 10;
        this.numericUpDownMaxChannels.Value = 3;

        this.labelGlobalDelay.Text = "Global Delay (ms):";
        this.labelGlobalDelay.Location = new Point(10, 55);
        this.labelGlobalDelay.Size = new Size(100, 23);

        this.numericUpDownGlobalDelay.Location = new Point(120, 52);
        this.numericUpDownGlobalDelay.Size = new Size(80, 23);
        this.numericUpDownGlobalDelay.Minimum = 1000;
        this.numericUpDownGlobalDelay.Maximum = 30000;
        this.numericUpDownGlobalDelay.Value = 5000;
        this.numericUpDownGlobalDelay.Increment = 500;

        this.checkBoxHeadlessMode.Text = "Headless Mode";
        this.checkBoxHeadlessMode.Location = new Point(10, 85);
        this.checkBoxHeadlessMode.Size = new Size(120, 23);

        this.checkBoxDomainValidation.Text = "Domain Validation";
        this.checkBoxDomainValidation.Location = new Point(140, 85);
        this.checkBoxDomainValidation.Size = new Size(130, 23);
        this.checkBoxDomainValidation.Checked = true;

        this.groupBoxGeneral.Controls.AddRange(new Control[] {
            this.labelMaxChannels, this.numericUpDownMaxChannels,
            this.labelGlobalDelay, this.numericUpDownGlobalDelay,
            this.checkBoxHeadlessMode, this.checkBoxDomainValidation
        });

        this.tabConfiguration.Controls.AddRange(new Control[] {
            this.groupBoxPaths, this.groupBoxGeneral
        });
    }

    #endregion

    // Control declarations
    private TabControl tabControl;
    private TabPage tabConfiguration;
    private TabPage tabFileProcessing;
    private TabPage tabPageProcessing;
    private TabPage tabChannelManagement;
    private TabPage tabLogs;
    private StatusStrip statusStrip;
    private ToolStripStatusLabel statusLabel;
    private ToolStripProgressBar progressBar;

    // Configuration Tab Controls
    private GroupBox groupBoxPaths;
    private Label labelSessionDataPath;
    private TextBox textBoxSessionDataPath;
    private Button buttonBrowseSessionData;
    private Label labelLogsPath;
    private TextBox textBoxLogsPath;
    private Button buttonBrowseLogs;
    private Label labelInputFilesPath;
    private TextBox textBoxInputFilesPath;
    private Button buttonBrowseInputFiles;

    private GroupBox groupBoxGeneral;
    private Label labelMaxChannels;
    private NumericUpDown numericUpDownMaxChannels;
    private Label labelGlobalDelay;
    private NumericUpDown numericUpDownGlobalDelay;
    private CheckBox checkBoxHeadlessMode;
    private CheckBox checkBoxDomainValidation;

    private GroupBox groupBoxChannels;
    private ListView listViewChannels;
    private Button buttonAddChannel;
    private Button buttonEditChannel;
    private Button buttonRemoveChannel;
    private Button buttonSaveConfig;
    private Button buttonLoadConfig;

    // File Processing Tab Controls
    private GroupBox groupBoxFileSelection;
    private Label labelSelectedFile;
    private TextBox textBoxSelectedFile;
    private Button buttonBrowseFile;
    private Button buttonValidateFile;
    private Button buttonProcessFile;

    private GroupBox groupBoxFileResults;
    private ListView listViewFileResults;
    private Label labelFileStats;

    // Page Processing Tab Controls
    private GroupBox groupBoxPageUrl;
    private Label labelPageUrl;
    private TextBox textBoxPageUrl;
    private Button buttonValidateUrl;
    private Button buttonProcessPage;

    private GroupBox groupBoxPageResults;
    private ListView listViewPageResults;
    private Label labelPageStats;

    // Channel Management Tab Controls
    private GroupBox groupBoxChannelControl;
    private Button buttonStartAllChannels;
    private Button buttonStopAllChannels;
    private Button buttonStartSelectedChannel;
    private Button buttonStopSelectedChannel;

    private GroupBox groupBoxChannelStatus;
    private ListView listViewChannelStatus;

    // Logs Tab Controls
    private GroupBox groupBoxLogDisplay;
    private RichTextBox richTextBoxLogs;
    private Button buttonClearLogs;
    private Button buttonSaveLogs;
    private ComboBox comboBoxLogLevel;
    private Label labelLogLevel;
}
