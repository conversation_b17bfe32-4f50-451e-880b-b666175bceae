﻿namespace TonLiker.Desktop;

partial class MainForm
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;



    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        this.tabControl = new TonLiker.Desktop.UI.ModernTabControl();
        this.tabConfiguration = new TabPage();
        this.tabFileProcessing = new TabPage();
        this.tabPageProcessing = new TabPage();
        this.tabChannelManagement = new TabPage();
        this.tabLogs = new TabPage();
        this.statusStrip = new StatusStrip();
        this.statusLabel = new ToolStripStatusLabel();
        this.progressBar = new ToolStripProgressBar();
        this.toolTip = new ToolTip();

        // Configuration Tab Controls
        this.groupBoxPaths = new GroupBox();
        this.labelSessionDataPath = new Label();
        this.textBoxSessionDataPath = new TextBox();
        this.buttonBrowseSessionData = new Button();
        this.labelLogsPath = new Label();
        this.textBoxLogsPath = new TextBox();
        this.buttonBrowseLogs = new Button();
        this.labelInputFilesPath = new Label();
        this.textBoxInputFilesPath = new TextBox();
        this.buttonBrowseInputFiles = new Button();

        this.groupBoxGeneral = new GroupBox();
        this.labelMaxChannels = new Label();
        this.numericUpDownMaxChannels = new NumericUpDown();
        this.labelGlobalDelay = new Label();
        this.numericUpDownGlobalDelay = new NumericUpDown();
        this.checkBoxHeadlessMode = new CheckBox();
        this.checkBoxDomainValidation = new CheckBox();

        this.groupBoxChannels = new GroupBox();
        this.listViewChannels = new ListView();
        this.buttonAddChannel = new Button();
        this.buttonEditChannel = new Button();
        this.buttonRemoveChannel = new Button();
        this.buttonSaveConfig = new Button();
        this.buttonLoadConfig = new Button();

        // File Processing Tab Controls
        this.groupBoxFileSelection = new GroupBox();
        this.labelSelectedFile = new Label();
        this.textBoxSelectedFile = new TextBox();
        this.buttonBrowseFile = new Button();
        this.buttonValidateFile = new Button();
        this.buttonProcessFile = new Button();

        this.groupBoxFileResults = new GroupBox();
        this.listViewFileResults = new ListView();
        this.labelFileStats = new Label();

        // Page Processing Tab Controls
        this.groupBoxPageUrl = new GroupBox();
        this.labelPageUrl = new Label();
        this.textBoxPageUrl = new TextBox();
        this.buttonValidateUrl = new Button();
        this.buttonProcessPage = new Button();

        this.groupBoxPageResults = new GroupBox();
        this.listViewPageResults = new ListView();
        this.labelPageStats = new Label();

        // Channel Management Tab Controls
        this.groupBoxChannelControl = new GroupBox();
        this.buttonStartAllChannels = new Button();
        this.buttonStopAllChannels = new Button();
        this.buttonStartSelectedChannel = new Button();
        this.buttonStopSelectedChannel = new Button();

        this.groupBoxChannelStatus = new GroupBox();
        this.listViewChannelStatus = new ListView();

        // Logs Tab Controls
        this.groupBoxLogDisplay = new GroupBox();
        this.richTextBoxLogs = new RichTextBox();
        this.buttonClearLogs = new Button();
        this.buttonSaveLogs = new Button();
        this.comboBoxLogLevel = new ComboBox();
        this.labelLogLevel = new Label();

        this.SuspendLayout();

        // Main Form
        this.AutoScaleDimensions = new SizeF(7F, 15F);
        this.AutoScaleMode = AutoScaleMode.Font;
        this.ClientSize = new Size(1200, 800);
        this.Text = "TonLiker Desktop - NFT Automation Tool";
        this.StartPosition = FormStartPosition.CenterScreen;
        this.MinimumSize = new Size(1000, 600);

        // Apply modern theme
        ApplyModernTheme();

        // Tab Control
        this.tabControl.Dock = DockStyle.Fill;
        this.tabControl.Location = new Point(0, 0);
        this.tabControl.Size = new Size(1200, 775);
        this.tabControl.TabIndex = 0;

        // Configuration Tab
        this.tabConfiguration.Text = "Configuration";
        this.tabConfiguration.UseVisualStyleBackColor = true;

        // Configuration Tab Layout
        SetupConfigurationTab();

        // File Processing Tab
        this.tabFileProcessing.Text = "File Processing";
        this.tabFileProcessing.UseVisualStyleBackColor = true;

        // File Processing Tab Layout
        SetupFileProcessingTab();

        // Page Processing Tab
        this.tabPageProcessing.Text = "Page Processing";
        this.tabPageProcessing.UseVisualStyleBackColor = true;

        // Page Processing Tab Layout
        SetupPageProcessingTab();

        // Channel Management Tab
        this.tabChannelManagement.Text = "Channel Management";
        this.tabChannelManagement.UseVisualStyleBackColor = true;

        // Channel Management Tab Layout
        SetupChannelManagementTab();

        // Logs Tab
        this.tabLogs.Text = "Logs";
        this.tabLogs.UseVisualStyleBackColor = true;

        // Logs Tab Layout
        SetupLogsTab();

        // Status Strip
        this.statusStrip.Items.AddRange(new ToolStripItem[] { this.statusLabel, this.progressBar });
        this.statusStrip.Location = new Point(0, 775);
        this.statusStrip.Size = new Size(1200, 25);
        this.statusStrip.TabIndex = 1;

        this.statusLabel.Text = "Ready";
        this.progressBar.Size = new Size(200, 16);

        // Add tabs to tab control
        this.tabControl.TabPages.Add(this.tabConfiguration);
        this.tabControl.TabPages.Add(this.tabFileProcessing);
        this.tabControl.TabPages.Add(this.tabPageProcessing);
        this.tabControl.TabPages.Add(this.tabChannelManagement);
        this.tabControl.TabPages.Add(this.tabLogs);

        // Add controls to form
        this.Controls.Add(this.tabControl);
        this.Controls.Add(this.statusStrip);

        this.ResumeLayout(false);
        this.PerformLayout();
    }

    private void SetupConfigurationTab()
    {
        // Paths Group Box
        this.groupBoxPaths.Text = "Paths";
        this.groupBoxPaths.Location = new Point(10, 10);
        this.groupBoxPaths.Size = new Size(560, 120);

        this.labelSessionDataPath.Text = "Session Data Path:";
        this.labelSessionDataPath.Location = new Point(10, 25);
        this.labelSessionDataPath.Size = new Size(120, 23);

        this.textBoxSessionDataPath.Location = new Point(140, 22);
        this.textBoxSessionDataPath.Size = new Size(350, 23);

        this.buttonBrowseSessionData.Text = "Browse";
        this.buttonBrowseSessionData.Location = new Point(500, 21);
        this.buttonBrowseSessionData.Size = new Size(50, 25);
        this.buttonBrowseSessionData.Click += ButtonBrowseSessionData_Click;

        this.labelLogsPath.Text = "Logs Path:";
        this.labelLogsPath.Location = new Point(10, 55);
        this.labelLogsPath.Size = new Size(120, 23);

        this.textBoxLogsPath.Location = new Point(140, 52);
        this.textBoxLogsPath.Size = new Size(350, 23);

        this.buttonBrowseLogs.Text = "Browse";
        this.buttonBrowseLogs.Location = new Point(500, 51);
        this.buttonBrowseLogs.Size = new Size(50, 25);
        this.buttonBrowseLogs.Click += ButtonBrowseLogs_Click;

        this.labelInputFilesPath.Text = "Input Files Path:";
        this.labelInputFilesPath.Location = new Point(10, 85);
        this.labelInputFilesPath.Size = new Size(120, 23);

        this.textBoxInputFilesPath.Location = new Point(140, 82);
        this.textBoxInputFilesPath.Size = new Size(350, 23);

        this.buttonBrowseInputFiles.Text = "Browse";
        this.buttonBrowseInputFiles.Location = new Point(500, 81);
        this.buttonBrowseInputFiles.Size = new Size(50, 25);
        this.buttonBrowseInputFiles.Click += ButtonBrowseInputFiles_Click;

        this.groupBoxPaths.Controls.AddRange(new Control[] {
            this.labelSessionDataPath, this.textBoxSessionDataPath, this.buttonBrowseSessionData,
            this.labelLogsPath, this.textBoxLogsPath, this.buttonBrowseLogs,
            this.labelInputFilesPath, this.textBoxInputFilesPath, this.buttonBrowseInputFiles
        });

        // General Settings Group Box
        this.groupBoxGeneral.Text = "General Settings";
        this.groupBoxGeneral.Location = new Point(580, 10);
        this.groupBoxGeneral.Size = new Size(300, 120);

        this.labelMaxChannels.Text = "Max Channels:";
        this.labelMaxChannels.Location = new Point(10, 25);
        this.labelMaxChannels.Size = new Size(100, 23);

        this.numericUpDownMaxChannels.Location = new Point(120, 22);
        this.numericUpDownMaxChannels.Size = new Size(60, 23);
        this.numericUpDownMaxChannels.Minimum = 1;
        this.numericUpDownMaxChannels.Maximum = 10;
        this.numericUpDownMaxChannels.Value = 3;

        this.labelGlobalDelay.Text = "Global Delay (ms):";
        this.labelGlobalDelay.Location = new Point(10, 55);
        this.labelGlobalDelay.Size = new Size(100, 23);

        this.numericUpDownGlobalDelay.Location = new Point(120, 52);
        this.numericUpDownGlobalDelay.Size = new Size(80, 23);
        this.numericUpDownGlobalDelay.Minimum = 1000;
        this.numericUpDownGlobalDelay.Maximum = 30000;
        this.numericUpDownGlobalDelay.Value = 5000;
        this.numericUpDownGlobalDelay.Increment = 500;

        this.checkBoxHeadlessMode.Text = "Headless Mode";
        this.checkBoxHeadlessMode.Location = new Point(10, 85);
        this.checkBoxHeadlessMode.Size = new Size(120, 23);

        this.checkBoxDomainValidation.Text = "Domain Validation";
        this.checkBoxDomainValidation.Location = new Point(140, 85);
        this.checkBoxDomainValidation.Size = new Size(130, 23);
        this.checkBoxDomainValidation.Checked = true;

        this.groupBoxGeneral.Controls.AddRange(new Control[] {
            this.labelMaxChannels, this.numericUpDownMaxChannels,
            this.labelGlobalDelay, this.numericUpDownGlobalDelay,
            this.checkBoxHeadlessMode, this.checkBoxDomainValidation
        });

        // Channels Group Box
        this.groupBoxChannels.Text = "Channels";
        this.groupBoxChannels.Location = new Point(10, 140);
        this.groupBoxChannels.Size = new Size(870, 200);

        this.listViewChannels.Location = new Point(10, 25);
        this.listViewChannels.Size = new Size(650, 130);
        this.listViewChannels.View = View.Details;
        this.listViewChannels.FullRowSelect = true;
        this.listViewChannels.GridLines = true;
        this.listViewChannels.Columns.Add("Name", 100);
        this.listViewChannels.Columns.Add("Enabled", 60);
        this.listViewChannels.Columns.Add("Action Delay", 80);
        this.listViewChannels.Columns.Add("Scroll Delay", 80);
        this.listViewChannels.Columns.Add("Max Retries", 80);
        this.listViewChannels.Columns.Add("Wallet Address", 200);
        this.listViewChannels.SelectedIndexChanged += ListViewChannels_SelectedIndexChanged;

        this.buttonAddChannel.Text = "Add Channel";
        this.buttonAddChannel.Location = new Point(670, 25);
        this.buttonAddChannel.Size = new Size(100, 30);
        this.buttonAddChannel.Click += ButtonAddChannel_Click;

        this.buttonEditChannel.Text = "Edit Channel";
        this.buttonEditChannel.Location = new Point(670, 60);
        this.buttonEditChannel.Size = new Size(100, 30);
        this.buttonEditChannel.Enabled = false;
        this.buttonEditChannel.Click += ButtonEditChannel_Click;

        this.buttonRemoveChannel.Text = "Remove Channel";
        this.buttonRemoveChannel.Location = new Point(670, 95);
        this.buttonRemoveChannel.Size = new Size(100, 30);
        this.buttonRemoveChannel.Enabled = false;
        this.buttonRemoveChannel.Click += ButtonRemoveChannel_Click;

        this.buttonSaveConfig.Text = "Save Configuration";
        this.buttonSaveConfig.Location = new Point(670, 165);
        this.buttonSaveConfig.Size = new Size(120, 30);
        this.buttonSaveConfig.Click += ButtonSaveConfig_Click;

        this.buttonLoadConfig.Text = "Reload Configuration";
        this.buttonLoadConfig.Location = new Point(800, 165);
        this.buttonLoadConfig.Size = new Size(130, 30);
        this.buttonLoadConfig.Click += ButtonLoadConfig_Click;

        this.groupBoxChannels.Controls.AddRange(new Control[] {
            this.listViewChannels, this.buttonAddChannel, this.buttonEditChannel,
            this.buttonRemoveChannel, this.buttonSaveConfig, this.buttonLoadConfig
        });

        this.tabConfiguration.Controls.AddRange(new Control[] {
            this.groupBoxPaths, this.groupBoxGeneral, this.groupBoxChannels
        });
    }

    private void SetupFileProcessingTab()
    {
        // File Selection Group Box
        this.groupBoxFileSelection.Text = "File Selection";
        this.groupBoxFileSelection.Location = new Point(10, 10);
        this.groupBoxFileSelection.Size = new Size(870, 80);

        this.labelSelectedFile.Text = "Selected File:";
        this.labelSelectedFile.Location = new Point(10, 25);
        this.labelSelectedFile.Size = new Size(80, 23);

        this.textBoxSelectedFile.Location = new Point(100, 22);
        this.textBoxSelectedFile.Size = new Size(500, 23);
        this.textBoxSelectedFile.ReadOnly = true;

        this.buttonBrowseFile.Text = "Browse";
        this.buttonBrowseFile.Location = new Point(610, 21);
        this.buttonBrowseFile.Size = new Size(70, 25);
        this.buttonBrowseFile.Click += ButtonBrowseFile_Click;

        this.buttonValidateFile.Text = "Validate";
        this.buttonValidateFile.Location = new Point(690, 21);
        this.buttonValidateFile.Size = new Size(70, 25);
        this.buttonValidateFile.Enabled = false;
        this.buttonValidateFile.Click += ButtonValidateFile_Click;

        this.buttonProcessFile.Text = "Process";
        this.buttonProcessFile.Location = new Point(770, 21);
        this.buttonProcessFile.Size = new Size(70, 25);
        this.buttonProcessFile.Enabled = false;
        this.buttonProcessFile.Click += ButtonProcessFile_Click;

        this.groupBoxFileSelection.Controls.AddRange(new Control[] {
            this.labelSelectedFile, this.textBoxSelectedFile, this.buttonBrowseFile,
            this.buttonValidateFile, this.buttonProcessFile
        });

        // File Results Group Box
        this.groupBoxFileResults.Text = "Results";
        this.groupBoxFileResults.Location = new Point(10, 100);
        this.groupBoxFileResults.Size = new Size(870, 400);

        this.listViewFileResults.Location = new Point(10, 25);
        this.listViewFileResults.Size = new Size(850, 320);
        this.listViewFileResults.View = View.Details;
        this.listViewFileResults.FullRowSelect = true;
        this.listViewFileResults.GridLines = true;
        this.listViewFileResults.Columns.Add("URL", 300);
        this.listViewFileResults.Columns.Add("Title", 150);
        this.listViewFileResults.Columns.Add("Collection", 150);
        this.listViewFileResults.Columns.Add("Status", 100);
        this.listViewFileResults.Columns.Add("Error", 200);

        this.labelFileStats.Text = "No file selected";
        this.labelFileStats.Location = new Point(10, 355);
        this.labelFileStats.Size = new Size(850, 40);
        this.labelFileStats.Font = new Font(this.labelFileStats.Font.FontFamily, 9, FontStyle.Bold);

        this.groupBoxFileResults.Controls.AddRange(new Control[] {
            this.listViewFileResults, this.labelFileStats
        });

        this.tabFileProcessing.Controls.AddRange(new Control[] {
            this.groupBoxFileSelection, this.groupBoxFileResults
        });
    }

    private void SetupPageProcessingTab()
    {
        // Page URL Group Box
        this.groupBoxPageUrl.Text = "Page URL";
        this.groupBoxPageUrl.Location = new Point(10, 10);
        this.groupBoxPageUrl.Size = new Size(870, 80);

        this.labelPageUrl.Text = "getgems.io URL:";
        this.labelPageUrl.Location = new Point(10, 25);
        this.labelPageUrl.Size = new Size(100, 23);

        this.textBoxPageUrl.Location = new Point(120, 22);
        this.textBoxPageUrl.Size = new Size(480, 23);
        this.textBoxPageUrl.PlaceholderText = "https://getgems.io/collection/example";

        this.buttonValidateUrl.Text = "Validate";
        this.buttonValidateUrl.Location = new Point(610, 21);
        this.buttonValidateUrl.Size = new Size(70, 25);
        this.buttonValidateUrl.Click += ButtonValidateUrl_Click;

        this.buttonProcessPage.Text = "Process";
        this.buttonProcessPage.Location = new Point(690, 21);
        this.buttonProcessPage.Size = new Size(70, 25);
        this.buttonProcessPage.Enabled = false;
        this.buttonProcessPage.Click += ButtonProcessPage_Click;

        this.groupBoxPageUrl.Controls.AddRange(new Control[] {
            this.labelPageUrl, this.textBoxPageUrl, this.buttonValidateUrl, this.buttonProcessPage
        });

        // Page Results Group Box
        this.groupBoxPageResults.Text = "Processing Results";
        this.groupBoxPageResults.Location = new Point(10, 100);
        this.groupBoxPageResults.Size = new Size(870, 400);

        this.listViewPageResults.Location = new Point(10, 25);
        this.listViewPageResults.Size = new Size(850, 320);
        this.listViewPageResults.View = View.Details;
        this.listViewPageResults.FullRowSelect = true;
        this.listViewPageResults.GridLines = true;
        this.listViewPageResults.Columns.Add("NFT URL", 300);
        this.listViewPageResults.Columns.Add("Title", 150);
        this.listViewPageResults.Columns.Add("Collection", 150);
        this.listViewPageResults.Columns.Add("Status", 100);
        this.listViewPageResults.Columns.Add("Channel", 100);
        this.listViewPageResults.Columns.Add("Timestamp", 120);

        this.labelPageStats.Text = "Enter a getgems.io URL to begin";
        this.labelPageStats.Location = new Point(10, 355);
        this.labelPageStats.Size = new Size(850, 40);
        this.labelPageStats.Font = new Font(this.labelPageStats.Font.FontFamily, 9, FontStyle.Bold);

        this.groupBoxPageResults.Controls.AddRange(new Control[] {
            this.listViewPageResults, this.labelPageStats
        });

        this.tabPageProcessing.Controls.AddRange(new Control[] {
            this.groupBoxPageUrl, this.groupBoxPageResults
        });
    }

    private void SetupChannelManagementTab()
    {
        // Channel Control Group Box
        this.groupBoxChannelControl.Text = "Channel Control";
        this.groupBoxChannelControl.Location = new Point(10, 10);
        this.groupBoxChannelControl.Size = new Size(870, 80);

        this.buttonStartAllChannels.Text = "Start All Channels";
        this.buttonStartAllChannels.Location = new Point(20, 30);
        this.buttonStartAllChannels.Size = new Size(120, 35);
        this.buttonStartAllChannels.Click += ButtonStartAllChannels_Click;

        this.buttonStopAllChannels.Text = "Stop All Channels";
        this.buttonStopAllChannels.Location = new Point(150, 30);
        this.buttonStopAllChannels.Size = new Size(120, 35);
        this.buttonStopAllChannels.Enabled = false;
        this.buttonStopAllChannels.Click += ButtonStopAllChannels_Click;

        this.buttonStartSelectedChannel.Text = "Start Selected";
        this.buttonStartSelectedChannel.Location = new Point(300, 30);
        this.buttonStartSelectedChannel.Size = new Size(120, 35);
        this.buttonStartSelectedChannel.Enabled = false;
        this.buttonStartSelectedChannel.Click += ButtonStartSelectedChannel_Click;

        this.buttonStopSelectedChannel.Text = "Stop Selected";
        this.buttonStopSelectedChannel.Location = new Point(430, 30);
        this.buttonStopSelectedChannel.Size = new Size(120, 35);
        this.buttonStopSelectedChannel.Enabled = false;
        this.buttonStopSelectedChannel.Click += ButtonStopSelectedChannel_Click;

        this.groupBoxChannelControl.Controls.AddRange(new Control[] {
            this.buttonStartAllChannels, this.buttonStopAllChannels,
            this.buttonStartSelectedChannel, this.buttonStopSelectedChannel
        });

        // Channel Status Group Box
        this.groupBoxChannelStatus.Text = "Channel Status";
        this.groupBoxChannelStatus.Location = new Point(10, 100);
        this.groupBoxChannelStatus.Size = new Size(870, 400);

        this.listViewChannelStatus.Location = new Point(10, 25);
        this.listViewChannelStatus.Size = new Size(850, 360);
        this.listViewChannelStatus.View = View.Details;
        this.listViewChannelStatus.FullRowSelect = true;
        this.listViewChannelStatus.GridLines = true;
        this.listViewChannelStatus.Columns.Add("Channel", 100);
        this.listViewChannelStatus.Columns.Add("Status", 80);
        this.listViewChannelStatus.Columns.Add("Start Time", 120);
        this.listViewChannelStatus.Columns.Add("Processed", 80);
        this.listViewChannelStatus.Columns.Add("Successful", 80);
        this.listViewChannelStatus.Columns.Add("Failed", 80);
        this.listViewChannelStatus.Columns.Add("Current URL", 300);
        this.listViewChannelStatus.SelectedIndexChanged += ListViewChannelStatus_SelectedIndexChanged;

        this.groupBoxChannelStatus.Controls.Add(this.listViewChannelStatus);

        this.tabChannelManagement.Controls.AddRange(new Control[] {
            this.groupBoxChannelControl, this.groupBoxChannelStatus
        });
    }

    private void SetupLogsTab()
    {
        // Log Display Group Box
        this.groupBoxLogDisplay.Text = "Application Logs";
        this.groupBoxLogDisplay.Location = new Point(10, 10);
        this.groupBoxLogDisplay.Size = new Size(870, 490);

        // Log Level Filter
        this.labelLogLevel.Text = "Log Level:";
        this.labelLogLevel.Location = new Point(10, 25);
        this.labelLogLevel.Size = new Size(70, 23);

        this.comboBoxLogLevel.Location = new Point(85, 22);
        this.comboBoxLogLevel.Size = new Size(100, 23);
        this.comboBoxLogLevel.DropDownStyle = ComboBoxStyle.DropDownList;
        this.comboBoxLogLevel.Items.AddRange(new[] { "All", "Information", "Warning", "Error" });
        this.comboBoxLogLevel.SelectedIndex = 0;
        this.comboBoxLogLevel.SelectedIndexChanged += ComboBoxLogLevel_SelectedIndexChanged;

        // Control Buttons
        this.buttonClearLogs.Text = "Clear Logs";
        this.buttonClearLogs.Location = new Point(700, 21);
        this.buttonClearLogs.Size = new Size(80, 25);
        this.buttonClearLogs.Click += ButtonClearLogs_Click;

        this.buttonSaveLogs.Text = "Save Logs";
        this.buttonSaveLogs.Location = new Point(790, 21);
        this.buttonSaveLogs.Size = new Size(70, 25);
        this.buttonSaveLogs.Click += ButtonSaveLogs_Click;

        // Log Display
        this.richTextBoxLogs.Location = new Point(10, 55);
        this.richTextBoxLogs.Size = new Size(850, 420);
        this.richTextBoxLogs.ReadOnly = true;
        this.richTextBoxLogs.Font = new Font("Consolas", 9);
        this.richTextBoxLogs.BackColor = Color.Black;
        this.richTextBoxLogs.ForeColor = Color.LightGray;
        this.richTextBoxLogs.ScrollBars = RichTextBoxScrollBars.Both;
        this.richTextBoxLogs.WordWrap = false;

        this.groupBoxLogDisplay.Controls.AddRange(new Control[] {
            this.labelLogLevel, this.comboBoxLogLevel, this.buttonClearLogs,
            this.buttonSaveLogs, this.richTextBoxLogs
        });

        this.tabLogs.Controls.Add(this.groupBoxLogDisplay);

        // Setup tooltips
        SetupTooltips();
    }

    private void SetupTooltips()
    {
        // Configuration tab tooltips
        this.toolTip.SetToolTip(this.textBoxSessionDataPath, "Directory where browser session data and profiles are stored");
        this.toolTip.SetToolTip(this.textBoxLogsPath, "Directory where application logs are saved");
        this.toolTip.SetToolTip(this.textBoxInputFilesPath, "Directory where input CSV/TXT files are located");
        this.toolTip.SetToolTip(this.numericUpDownMaxChannels, "Maximum number of browser instances to run simultaneously");
        this.toolTip.SetToolTip(this.numericUpDownGlobalDelay, "Delay in milliseconds between starting each channel");
        this.toolTip.SetToolTip(this.checkBoxHeadlessMode, "Run browsers without visible windows (background mode)");
        this.toolTip.SetToolTip(this.checkBoxDomainValidation, "Validate that all URLs belong to allowed domains");
        this.toolTip.SetToolTip(this.buttonAddChannel, "Add a new browser channel configuration");
        this.toolTip.SetToolTip(this.buttonEditChannel, "Edit the selected channel configuration");
        this.toolTip.SetToolTip(this.buttonRemoveChannel, "Remove the selected channel configuration");
        this.toolTip.SetToolTip(this.buttonSaveConfig, "Save current configuration to appsettings.json");
        this.toolTip.SetToolTip(this.buttonLoadConfig, "Reload configuration from appsettings.json");

        // File processing tab tooltips
        this.toolTip.SetToolTip(this.buttonBrowseFile, "Select a CSV or TXT file containing NFT URLs");
        this.toolTip.SetToolTip(this.buttonValidateFile, "Validate the selected file and check URL formats");
        this.toolTip.SetToolTip(this.buttonProcessFile, "Start processing NFTs from the validated file");

        // Page processing tab tooltips
        this.toolTip.SetToolTip(this.textBoxPageUrl, "Enter a getgems.io collection or page URL");
        this.toolTip.SetToolTip(this.buttonValidateUrl, "Validate the entered URL format and accessibility");
        this.toolTip.SetToolTip(this.buttonProcessPage, "Start processing all NFTs found on the page");

        // Channel management tab tooltips
        this.toolTip.SetToolTip(this.buttonStartAllChannels, "Start all enabled browser channels");
        this.toolTip.SetToolTip(this.buttonStopAllChannels, "Stop all active browser channels");
        this.toolTip.SetToolTip(this.buttonStartSelectedChannel, "Start the selected channel only");
        this.toolTip.SetToolTip(this.buttonStopSelectedChannel, "Stop the selected channel only");

        // Logs tab tooltips
        this.toolTip.SetToolTip(this.comboBoxLogLevel, "Filter logs by minimum level");
        this.toolTip.SetToolTip(this.buttonClearLogs, "Clear all logs from the display");
        this.toolTip.SetToolTip(this.buttonSaveLogs, "Save current logs to a text file");
    }

    #endregion

    // Control declarations
    private TabControl tabControl;
    private TabPage tabConfiguration;
    private TabPage tabFileProcessing;
    private TabPage tabPageProcessing;
    private TabPage tabChannelManagement;
    private TabPage tabLogs;
    private StatusStrip statusStrip;
    private ToolStripStatusLabel statusLabel;
    private ToolStripProgressBar progressBar;

    // Configuration Tab Controls
    private GroupBox groupBoxPaths;
    private Label labelSessionDataPath;
    private TextBox textBoxSessionDataPath;
    private Button buttonBrowseSessionData;
    private Label labelLogsPath;
    private TextBox textBoxLogsPath;
    private Button buttonBrowseLogs;
    private Label labelInputFilesPath;
    private TextBox textBoxInputFilesPath;
    private Button buttonBrowseInputFiles;

    private GroupBox groupBoxGeneral;
    private Label labelMaxChannels;
    private NumericUpDown numericUpDownMaxChannels;
    private Label labelGlobalDelay;
    private NumericUpDown numericUpDownGlobalDelay;
    private CheckBox checkBoxHeadlessMode;
    private CheckBox checkBoxDomainValidation;

    private GroupBox groupBoxChannels;
    private ListView listViewChannels;
    private Button buttonAddChannel;
    private Button buttonEditChannel;
    private Button buttonRemoveChannel;
    private Button buttonSaveConfig;
    private Button buttonLoadConfig;

    // File Processing Tab Controls
    private GroupBox groupBoxFileSelection;
    private Label labelSelectedFile;
    private TextBox textBoxSelectedFile;
    private Button buttonBrowseFile;
    private Button buttonValidateFile;
    private Button buttonProcessFile;

    private GroupBox groupBoxFileResults;
    private ListView listViewFileResults;
    private Label labelFileStats;

    // Page Processing Tab Controls
    private GroupBox groupBoxPageUrl;
    private Label labelPageUrl;
    private TextBox textBoxPageUrl;
    private Button buttonValidateUrl;
    private Button buttonProcessPage;

    private GroupBox groupBoxPageResults;
    private ListView listViewPageResults;
    private Label labelPageStats;

    // Channel Management Tab Controls
    private GroupBox groupBoxChannelControl;
    private Button buttonStartAllChannels;
    private Button buttonStopAllChannels;
    private Button buttonStartSelectedChannel;
    private Button buttonStopSelectedChannel;

    private GroupBox groupBoxChannelStatus;
    private ListView listViewChannelStatus;

    // Logs Tab Controls
    private GroupBox groupBoxLogDisplay;
    private RichTextBox richTextBoxLogs;
    private Button buttonClearLogs;
    private Button buttonSaveLogs;
    private ComboBox comboBoxLogLevel;
    private Label labelLogLevel;
    private ToolTip toolTip;
}
