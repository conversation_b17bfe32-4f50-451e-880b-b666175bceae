namespace TonLiker.Desktop.Models;

/// <summary>
/// Represents an NFT item to be liked
/// </summary>
public class NftItem
{
    public string Url { get; set; } = string.Empty;
    public string? Title { get; set; }
    public string? Collection { get; set; }
    public bool IsValidated { get; set; } = false;
    public bool IsLiked { get; set; } = false;
    public DateTime? LastAttempt { get; set; }
    public int AttemptCount { get; set; } = 0;
    public string? ErrorMessage { get; set; }
    public NftStatus Status { get; set; } = NftStatus.Pending;
}

/// <summary>
/// Status of NFT processing
/// </summary>
public enum NftStatus
{
    Pending,
    Validated,
    Liked,
    Failed,
    Skipped,
    AlreadyLiked
}
