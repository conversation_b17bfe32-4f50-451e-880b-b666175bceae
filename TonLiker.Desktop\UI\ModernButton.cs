using System.Drawing.Drawing2D;

namespace TonLiker.Desktop.UI;

/// <summary>
/// Modern styled button with rounded corners and hover effects
/// </summary>
public class ModernButton : Button
{
    private bool _isHovered = false;
    private bool _isPressed = false;
    private bool _isPrimary = false;
    private int _borderRadius = ModernTheme.BorderRadiusMD;

    public bool IsPrimary
    {
        get => _isPrimary;
        set
        {
            _isPrimary = value;
            Invalidate();
        }
    }

    public int BorderRadius
    {
        get => _borderRadius;
        set
        {
            _borderRadius = value;
            Invalidate();
        }
    }

    public ModernButton()
    {
        SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | 
                 ControlStyles.ResizeRedraw | ControlStyles.DoubleBuffer, true);
        
        FlatStyle = FlatStyle.Flat;
        FlatAppearance.BorderSize = 0;
        Font = ModernTheme.FontBody;
        Cursor = Cursors.Hand;
        Size = new Size(100, 35);
    }

    protected override void OnPaint(PaintEventArgs e)
    {
        e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
        
        var rect = new Rectangle(0, 0, Width - 1, Height - 1);
        var path = GetRoundedRectanglePath(rect, _borderRadius);

        // Background
        using (var brush = new SolidBrush(GetBackgroundColor()))
        {
            e.Graphics.FillPath(brush, path);
        }

        // Border
        using (var pen = new Pen(GetBorderColor(), 1))
        {
            e.Graphics.DrawPath(pen, path);
        }

        // Text
        var textColor = GetTextColor();
        using (var brush = new SolidBrush(textColor))
        {
            var stringFormat = new StringFormat
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Center
            };
            
            e.Graphics.DrawString(Text, Font, brush, ClientRectangle, stringFormat);
        }

        path.Dispose();
    }

    private GraphicsPath GetRoundedRectanglePath(Rectangle rect, int radius)
    {
        var path = new GraphicsPath();
        
        if (radius <= 0)
        {
            path.AddRectangle(rect);
            return path;
        }

        int diameter = radius * 2;
        var arcRect = new Rectangle(rect.Location, new Size(diameter, diameter));

        // Top left arc
        path.AddArc(arcRect, 180, 90);

        // Top right arc
        arcRect.X = rect.Right - diameter;
        path.AddArc(arcRect, 270, 90);

        // Bottom right arc
        arcRect.Y = rect.Bottom - diameter;
        path.AddArc(arcRect, 0, 90);

        // Bottom left arc
        arcRect.X = rect.Left;
        path.AddArc(arcRect, 90, 90);

        path.CloseFigure();
        return path;
    }

    private Color GetBackgroundColor()
    {
        if (!Enabled)
            return ModernTheme.BackgroundTertiary;
            
        if (_isPrimary)
        {
            if (_isPressed)
                return ModernTheme.PrimaryDark;
            if (_isHovered)
                return ModernTheme.PrimaryLight;
            return ModernTheme.Primary;
        }
        else
        {
            if (_isPressed)
                return ModernTheme.BackgroundTertiary;
            if (_isHovered)
                return ModernTheme.Surface;
            return ModernTheme.BackgroundSecondary;
        }
    }

    private Color GetBorderColor()
    {
        if (!Enabled)
            return ModernTheme.BorderPrimary;
            
        if (_isPrimary)
            return _isHovered ? ModernTheme.PrimaryLight : ModernTheme.Primary;
        else
            return _isHovered ? ModernTheme.BorderSecondary : ModernTheme.BorderPrimary;
    }

    private Color GetTextColor()
    {
        if (!Enabled)
            return ModernTheme.TextDisabled;
            
        if (_isPrimary)
            return Color.White;
        else
            return ModernTheme.TextPrimary;
    }

    protected override void OnMouseEnter(EventArgs e)
    {
        base.OnMouseEnter(e);
        _isHovered = true;
        Invalidate();
    }

    protected override void OnMouseLeave(EventArgs e)
    {
        base.OnMouseLeave(e);
        _isHovered = false;
        _isPressed = false;
        Invalidate();
    }

    protected override void OnMouseDown(MouseEventArgs e)
    {
        base.OnMouseDown(e);
        _isPressed = true;
        Invalidate();
    }

    protected override void OnMouseUp(MouseEventArgs e)
    {
        base.OnMouseUp(e);
        _isPressed = false;
        Invalidate();
    }

    protected override void OnEnabledChanged(EventArgs e)
    {
        base.OnEnabledChanged(e);
        Invalidate();
    }
}
