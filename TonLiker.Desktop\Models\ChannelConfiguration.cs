namespace TonLiker.Desktop.Models;

/// <summary>
/// Configuration for a single channel (browser instance)
/// </summary>
public class ChannelConfiguration
{
    public string Name { get; set; } = string.Empty;
    public string ProfilePath { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = true;
    public int DelayBetweenActions { get; set; } = 1000; // milliseconds
    public int ScrollDelay { get; set; } = 2000; // milliseconds
    public int MaxRetries { get; set; } = 3;
    public string? TonWalletAddress { get; set; }
    public Dictionary<string, string> CustomHeaders { get; set; } = new();
    public List<string> UserAgents { get; set; } = new();
}
