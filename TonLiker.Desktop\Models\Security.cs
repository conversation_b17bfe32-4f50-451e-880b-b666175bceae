namespace TonLiker.Desktop.Models;

/// <summary>
/// Represents a security threat
/// </summary>
public class SecurityThreat
{
    public ThreatType Type { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public ThreatSeverity Severity { get; set; }
    public DateTime DetectedAt { get; set; } = DateTime.UtcNow;
}

public enum ThreatType
{
    UnauthorizedDomain,
    SuspiciousContent,
    WalletInteraction,
    PhishingAttempt,
    MaliciousScript
}

public enum ThreatSeverity
{
    Low,
    Medium,
    High,
    Critical
}
